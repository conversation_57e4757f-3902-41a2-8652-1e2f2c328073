# TopTabs (Shared)

A brand-consistent, reusable top tabs component for screen-level tab navigation.

- No gradients; uses solid brand colors
- Rounded corners, subtle look per design system
- Emoji or icon components (lucide) supported
- Accessible (role=tab, selected state)

## Import

```tsx
import { TopTabs } from '../../components/shared';
```

## Props

- tabs: Array<{ key: string; label: string; icon?: React.ReactNode | string; badgeCount?: number }>
- activeKey: string
- onChange: (key: string) => void
- backgroundColor?: string (default: colors.secondary // #CBC3E3)
- textColorActive?: string (default: colors.textPrimary)
- textColorInactive?: string (default: colors.white)
- containerStyle?: ViewStyle
- tabStyle?: ViewStyle
- labelStyle?: TextStyle
- size?: 'sm' | 'md' | 'lg' (default: 'md')

## Usage Examples

### Date Night
```tsx
<TopTabs
  backgroundColor={colors.secondary}
  tabs=[
    { key: 'discover', label: 'Discover', icon: '✨' },
    { key: 'my-ideas', label: 'Our Ideas', icon: '💕' },
    { key: 'meals', label: 'Meals', icon: '🍽️' },
  ]
  activeKey={activeTab}
  onChange={(key) => setActiveTab(key as TabType)}
/>
```

### Meals (icon components)
```tsx
<TopTabs
  backgroundColor={colors.secondary}
  containerStyle={{ marginHorizontal: 20, marginTop: 20 }}
  tabs=[
    { key: 'ideas', label: 'Ideas', icon: <Utensils size={20} /> },
    { key: 'voting', label: 'Voting', icon: <Heart size={20} /> },
    { key: 'history', label: 'History', icon: <History size={20} /> },
  ]
  activeKey={activeTab}
  onChange={(key) => setActiveTab(key as TabType)}
/>
```

## Brand and Design Notes

- Default background uses Secondary Lavender Purple (#CBC3E3). Override if required per screen.
- Active tab pill uses solid white background; text color defaults to colors.textPrimary.
- Inactive tab text defaults to white for contrast on purple background.
- Follow design system: solid colors only, rounded corners, subtle shadows (applied at container level if needed by the screen).

## Migration Guidance

- Prefer TopTabs over older TabContainer/TabButton.
- When migrating custom tab headers (e.g., Meals), remove duplicated local styles and rely on TopTabs.
- For icons, prefer using lucide-react-native components; emojis remain supported.

