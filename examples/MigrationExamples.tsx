/**
 * Migration Examples: How to update existing components to use the unified favorites system
 * 
 * This file shows before/after examples of how to migrate components from the old
 * inconsistent patterns to the new unified hook system.
 */

import React from 'react';
import { View, Alert } from 'react-native';
import { HeartToggle } from '../components/shared/HeartToggle';
import { useMealFavorites, useMemoryFavorites, useDateNightFavorites } from '../hooks/shared/useContentFavorites';
import { useAuth } from '../hooks/useAuth';

// ============================================================================
// EXAMPLE 1: Migrating Meal Components
// ============================================================================

// BEFORE: Old inconsistent pattern from Meals.tsx
const OldMealCard_BEFORE = ({ meal }: { meal: any }) => {
  const { user } = useAuth();
  
  const handleMealSave = async (item: any) => {
    if (!user?.id) return;
    
    try {
      await favoritesService.toggleFavorite(user.id, item.id, 'meal', true);
      Alert.alert('Saved!', `${item.title} has been added to your favorites!`);
    } catch (error) {
      console.error('Failed to save meal:', error);
      Alert.alert('Error', 'Failed to save meal. Please try again.');
    }
  };

  return (
    <View>
      {/* Old pattern: manual error handling, no optimistic updates */}
      <HeartToggle
        isFavorited={meal.isFavorite || false}
        onToggle={async (newFavoritedState) => {
          try {
            if (!user?.id) throw new Error('User not authenticated');
            await favoritesService.toggleFavorite(user.id, meal.id, 'meal', newFavoritedState);
            console.log(`Meal ${meal.id} ${newFavoritedState ? 'favorited' : 'unfavorited'}`);
          } catch (error) {
            console.error('Failed to toggle meal favorite:', error);
            Alert.alert('Error', 'Failed to update favorite. Please try again.');
          }
        }}
      />
    </View>
  );
};

// AFTER: New unified pattern
const NewMealCard_AFTER = ({ meal }: { meal: any }) => {
  const { toggleFavorite, isFavorited, loadingStates } = useMealFavorites({
    initialFavoriteIds: meal.isFavorite ? [meal.id] : [],
    onSuccess: (itemId, isFavorited) => {
      // Optional: Custom success handling
      console.log(`Meal ${itemId} ${isFavorited ? 'favorited' : 'unfavorited'}`);
    },
    onError: (itemId, error) => {
      // Optional: Custom error handling
      Alert.alert('Error', 'Failed to update favorite. Please try again.');
    },
  });

  return (
    <View>
      {/* New pattern: automatic error handling, optimistic updates, offline support */}
      <HeartToggle
        isFavorited={isFavorited(meal.id)}
        onToggle={async (newFavoritedState) => {
          await toggleFavorite(meal.id, {
            title: meal.title,
            category: meal.category,
          });
        }}
        disabled={loadingStates[meal.id]}
      />
    </View>
  );
};

// ============================================================================
// EXAMPLE 2: Migrating Scrapbook Memory Components
// ============================================================================

// BEFORE: Old TODO implementation from scrapbook.tsx
const OldMemoryCard_BEFORE = ({ memory }: { memory: any }) => {
  const handleMemoryLike = async (memoryId: string) => {
    try {
      // TODO: Implement actual like functionality
      Alert.alert(
        'Memory Liked! ❤️',
        'This memory has been added to your favorites!',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error liking memory:', error);
      Alert.alert('Error', 'Unable to like this memory. Please try again.');
    }
  };

  return (
    <View>
      {/* Old pattern: placeholder implementation */}
      <HeartToggle
        isFavorited={false} // Always false in old implementation
        onToggle={async () => {
          await handleMemoryLike(memory.id);
        }}
      />
    </View>
  );
};

// AFTER: New fully functional implementation
const NewMemoryCard_AFTER = ({ memory }: { memory: any }) => {
  const { toggleFavorite, isFavorited, loadingStates } = useMemoryFavorites({
    onSuccess: (itemId, isFavorited) => {
      Alert.alert(
        isFavorited ? 'Memory Liked! ❤️' : 'Memory Unliked',
        isFavorited 
          ? 'This memory has been added to your favorites!'
          : 'This memory has been removed from your favorites.',
        [{ text: 'OK' }]
      );
    },
  });

  return (
    <View>
      {/* New pattern: fully functional with real persistence */}
      <HeartToggle
        isFavorited={isFavorited(memory.id)}
        onToggle={async (newFavoritedState) => {
          await toggleFavorite(memory.id, {
            title: memory.title,
            date: memory.date,
            type: memory.type,
          });
        }}
        disabled={loadingStates[memory.id]}
      />
    </View>
  );
};

// ============================================================================
// EXAMPLE 3: Migrating Date Night Components
// ============================================================================

// BEFORE: Old pattern with mixed service usage
const OldDateNightCard_BEFORE = ({ idea }: { idea: any }) => {
  const { user } = useAuth();

  return (
    <View>
      <HeartToggle
        isFavorited={idea.isFavorite || false}
        onToggle={async (newFavoritedState) => {
          try {
            if (!user?.id) throw new Error('User not authenticated');
            // Mixed usage: sometimes favoritesService, sometimes dateNightIdeasService
            await favoritesService.toggleFavorite(user.id, idea.id, 'date_night', newFavoritedState);
            console.log(`Date night idea ${idea.id} ${newFavoritedState ? 'favorited' : 'unfavorited'}`);
          } catch (error) {
            console.error('Failed to toggle date night favorite:', error);
            Alert.alert('Error', 'Failed to update favorite. Please try again.');
          }
        }}
      />
    </View>
  );
};

// AFTER: New consistent pattern
const NewDateNightCard_AFTER = ({ idea }: { idea: any }) => {
  const { toggleFavorite, isFavorited, loadingStates } = useDateNightFavorites({
    initialFavoriteIds: idea.isFavorite ? [idea.id] : [],
  });

  return (
    <View>
      <HeartToggle
        isFavorited={isFavorited(idea.id)}
        onToggle={async (newFavoritedState) => {
          await toggleFavorite(idea.id, {
            title: idea.title,
            category: idea.category,
            cost: idea.cost,
            difficulty: idea.difficulty,
          });
        }}
        disabled={loadingStates[idea.id]}
      />
    </View>
  );
};

// ============================================================================
// EXAMPLE 4: Batch Operations and Advanced Usage
// ============================================================================

const AdvancedUsageExample = () => {
  const mealFavorites = useMealFavorites({ autoLoad: true });
  const memoryFavorites = useMemoryFavorites({ autoLoad: true });

  // Example: Check multiple items at once
  const checkMultipleMeals = async (mealIds: string[]) => {
    await mealFavorites.checkMultipleFavorites(mealIds);
  };

  // Example: Get all favorite items
  const getAllFavoriteMeals = () => {
    return mealFavorites.favoriteItems;
  };

  // Example: Update metadata for a favorite
  const addNoteToFavorite = async (itemId: string, note: string) => {
    await mealFavorites.updateMetadata(itemId, { note, updatedAt: Date.now() });
  };

  return (
    <View>
      {/* Your component UI */}
    </View>
  );
};

export {
  OldMealCard_BEFORE,
  NewMealCard_AFTER,
  OldMemoryCard_BEFORE,
  NewMemoryCard_AFTER,
  OldDateNightCard_BEFORE,
  NewDateNightCard_AFTER,
  AdvancedUsageExample,
};
