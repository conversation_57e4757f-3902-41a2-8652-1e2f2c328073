import React from 'react';
import { render } from '@testing-library/react-native';
import { <PERSON><PERSON> as DSButton } from '../components/design-system/Templates';
import { Shuffle } from 'lucide-react-native';

describe('DSButton', () => {
  it('renders title', () => {
    const { getByText } = render(
      <DSButton title="Click Me" onPress={() => {}} />
    );
    expect(getByText('Click Me')).toBeTruthy();
  });

  it('renders left icon when provided', () => {
    const { getByLabelText } = render(
      <DSButton title="Icon Btn" onPress={() => {}} leftIcon={<Shuffle accessibilityLabel="icon" />} />
    );
    expect(getByLabelText('icon')).toBeTruthy();
  });
});

