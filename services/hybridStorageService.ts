import { secureStorage } from '../utils/secureStorage';
import { dataService } from './dataService';
import { auth } from './auth';
import { logger } from '../utils/logger';

/**
 * Hybrid Storage Service
 * 
 * This service provides a seamless interface between local storage and Supabase.
 * It automatically syncs data when the user is authenticated and falls back
 * to local storage when offline or not authenticated.
 */
class HybridStorageService {
  private isOnline: boolean = true;
  private isAuthenticated: boolean = false;

  constructor() {
    this.checkAuthStatus();
  }

  /**
   * Check if user is authenticated
   */
  private async checkAuthStatus() {
    try {
      this.isAuthenticated = !!(await auth.getCurrent());
    } catch (error) {
      logger.error('Error checking auth status:', error);
      this.isAuthenticated = false;
    }
  }

  /**
   * Set online status
   */
  setOnlineStatus(isOnline: boolean) {
    this.isOnline = isOnline;
  }

  /**
   * Get data with automatic fallback
   */
  async getData<T>(key: string, fallbackValue: T): Promise<T> {
    try {
      // If authenticated and online, try Supabase first
      if (this.isAuthenticated && this.isOnline) {
        const cloudData = await this.getFromSupabase<T>(key);
        if (cloudData !== null) {
          // Sync to local storage as backup
          await secureStorage.setItem(key, cloudData);
          return cloudData;
        }
      }

      // Fallback to local storage
      const localData = await secureStorage.getItem<T>(key);
      return localData || fallbackValue;
    } catch (error) {
      logger.error('Error getting data:', error);
      // Final fallback to local storage
      const localData = await secureStorage.getItem<T>(key);
      return localData || fallbackValue;
    }
  }

  /**
   * Save data with automatic sync
   */
  async saveData<T>(key: string, data: T): Promise<boolean> {
    try {
      // Always save to local storage first
      await secureStorage.setItem(key, data);

      // If authenticated and online, sync to Supabase
      if (this.isAuthenticated && this.isOnline) {
        const success = await this.saveToSupabase(key, data);
        if (!success) {
          logger.warn('Failed to sync to Supabase, data saved locally only');
        }
        return success;
      }

      return true; // Saved locally
    } catch (error) {
      logger.error('Error saving data:', error);
      return false;
    }
  }

  /**
   * Get data from Supabase based on key
   */
  private async getFromSupabase<T>(key: string): Promise<T | null> {
    try {
      switch (key) {
        case 'user_profile':
          const uid = await this.getCurrentUserId();
          const profile = uid ? await auth.getUserProfile(uid) : null;
          return profile as T;

        case 'week_one_data':
          const weekOneData = await dataService.getWeeklyData(1);
          return weekOneData?.data as T;

        case 'week_two_data':
          const weekTwoData = await dataService.getWeeklyData(2);
          return weekTwoData?.data as T;

        case 'week_three_data':
          const weekThreeData = await dataService.getWeeklyData(3);
          return weekThreeData?.data as T;

        case 'week_four_data':
          const weekFourData = await dataService.getWeeklyData(4);
          return weekFourData?.data as T;

        case 'week_five_data':
          const weekFiveData = await dataService.getWeeklyData(5);
          return weekFiveData?.data as T;

        case 'week_six_data':
          const weekSixData = await dataService.getWeeklyData(6);
          return weekSixData?.data as T;

        case 'week_seven_data':
          const weekSevenData = await dataService.getWeeklyData(7);
          return weekSevenData?.data as T;

        case 'week_eight_data':
          const weekEightData = await dataService.getWeeklyData(8);
          return weekEightData?.data as T;

        case 'week_nine_data':
          const weekNineData = await dataService.getWeeklyData(9);
          return weekNineData?.data as T;

        case 'week_ten_data':
          const weekTenData = await dataService.getWeeklyData(10);
          return weekTenData?.data as T;

        case 'week_eleven_data':
          const weekElevenData = await dataService.getWeeklyData(11);
          return weekElevenData?.data as T;

        case 'week_twelve_data':
          const weekTwelveData = await dataService.getWeeklyData(12);
          return weekTwelveData?.data as T;

        case 'origin_story_data':
          const originStory = await dataService.getOriginStory();
          return originStory?.data as T;

        case 'points_system_data':
          const pointsSystem = await dataService.getPointsSystem();
          return pointsSystem as T;

        case 'date_night_ideas_data':
          const dateNightIdeas = await dataService.getDateNightIdeas();
          return dateNightIdeas as T;

        case 'meal_voting_data':
          const mealVoting = await dataService.getMealVoting();
          return mealVoting as T;

        case 'scrapbook_data':
          const scrapbook = await dataService.getScrapbook();
          return scrapbook as T;

        default:
          logger.warn('Unknown key for Supabase sync:', key);
          return null;
      }
    } catch (error) {
      logger.error('Error getting data from Supabase:', error);
      return null;
    }
  }

  /**
   * Save data to Supabase based on key
   */
  private async saveToSupabase<T>(key: string, data: T): Promise<boolean> {
    try {
      switch (key) {
        case 'user_profile':
          const userId = await this.getCurrentUserId();
          if (!userId) return false;
          await auth.updateUserProfile(userId, data as any);
          return true;

        case 'week_one_data':
          return await dataService.saveWeeklyData(1, data, (data as any).completedSections, (data as any).completedAt);

        case 'week_two_data':
          return await dataService.saveWeeklyData(2, data, (data as any).completedSections, (data as any).completedAt);

        case 'week_three_data':
          return await dataService.saveWeeklyData(3, data, (data as any).completedSections, (data as any).completedAt);

        case 'week_four_data':
          return await dataService.saveWeeklyData(4, data, (data as any).completedSections, (data as any).completedAt);

        case 'week_five_data':
          return await dataService.saveWeeklyData(5, data, (data as any).completedSections, (data as any).completedAt);

        case 'week_six_data':
          return await dataService.saveWeeklyData(6, data, (data as any).completedSections, (data as any).completedAt);

        case 'week_seven_data':
          return await dataService.saveWeeklyData(7, data, (data as any).completedSections, (data as any).completedAt);

        case 'week_eight_data':
          return await dataService.saveWeeklyData(8, data, (data as any).completedSections, (data as any).completedAt);

        case 'week_nine_data':
          return await dataService.saveWeeklyData(9, data, (data as any).completedSections, (data as any).completedAt);

        case 'week_ten_data':
          return await dataService.saveWeeklyData(10, data, (data as any).completedSections, (data as any).completedAt);

        case 'week_eleven_data':
          return await dataService.saveWeeklyData(11, data, (data as any).completedSections, (data as any).completedAt);

        case 'week_twelve_data':
          return await dataService.saveWeeklyData(12, data, (data as any).completedSections, (data as any).completedAt);

        case 'origin_story_data':
          return await dataService.saveOriginStory(data);

        case 'points_system_data':
          // TODO: Implement points system data saving with Supabase
          console.log('Points system data saving not yet implemented with Supabase');
          return true;

        case 'date_night_ideas_data':
          const ideasData = data as any;
          return await dataService.saveDateNightIdeas(ideasData.ideas, ideasData.favorites, ideasData.completed);

        case 'meal_voting_data':
          const mealData = data as any;
          return await dataService.saveMealVoting(mealData.history, mealData.preferences);

        case 'scrapbook_data':
          return await dataService.saveScrapbook(data as any);

        default:
          logger.warn('Unknown key for Supabase sync:', key);
          return false;
      }
    } catch (error) {
      logger.error('Error saving data to Supabase:', error);
      return false;
    }
  }

  /**
   * Get current user ID
   */
  private async getCurrentUserId(): Promise<string | null> {
    try {
      const user = await auth.getCurrentUserData();
      return user?.user?.id || null;
    } catch (error) {
      logger.error('Error getting current user ID:', error);
      return null;
    }
  }

  /**
   * Sync all local data to Supabase
   */
  async syncAllLocalData(): Promise<boolean> {
    try {
      if (!this.isAuthenticated || !this.isOnline) {
        logger.warn('Cannot sync: not authenticated or offline');
        return false;
      }

      logger.info('Starting full data sync to Supabase');

      // List of all data keys to sync
      const dataKeys = [
        'user_profile',
        'week_one_data',
        'week_two_data',
        'week_three_data',
        'week_four_data',
        'week_five_data',
        'week_six_data',
        'week_seven_data',
        'week_eight_data',
        'week_nine_data',
        'week_ten_data',
        'week_eleven_data',
        'week_twelve_data',
        'origin_story_data',
        'points_system_data',
        'date_night_ideas_data',
        'meal_voting_data',
        'scrapbook_data',
      ];

      let successCount = 0;
      for (const key of dataKeys) {
        try {
          const localData = await secureStorage.getItem(key);
          if (localData) {
            const success = await this.saveToSupabase(key, localData);
            if (success) {
              successCount++;
            }
          }
        } catch (error) {
          logger.error(`Error syncing ${key}:`, error);
        }
      }

      logger.info(`Data sync completed: ${successCount}/${dataKeys.length} items synced`);
      return successCount > 0;
    } catch (error) {
      logger.error('Error syncing all data:', error);
      return false;
    }
  }

  /**
   * Clear all local data (useful for logout)
   */
  async clearAllLocalData(): Promise<boolean> {
    try {
      const dataKeys = [
        'user_profile',
        'week_one_data',
        'week_two_data',
        'week_three_data',
        'week_four_data',
        'week_five_data',
        'week_six_data',
        'week_seven_data',
        'week_eight_data',
        'week_nine_data',
        'week_ten_data',
        'week_eleven_data',
        'week_twelve_data',
        'origin_story_data',
        'points_system_data',
        'date_night_ideas_data',
        'meal_voting_data',
        'scrapbook_data',
      ];

      for (const key of dataKeys) {
        await secureStorage.removeItem(key);
      }

      logger.info('All local data cleared');
      return true;
    } catch (error) {
      logger.error('Error clearing local data:', error);
      return false;
    }
  }
}

export const hybridStorageService = new HybridStorageService();
export default hybridStorageService;
