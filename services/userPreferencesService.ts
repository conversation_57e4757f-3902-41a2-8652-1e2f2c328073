import { supabase } from '../lib/supabase/client';
import { logger } from '../utils/logger';

export interface UserPreferences {
  id: string;
  user_id: string;
  hidden_date_night_ideas: string[];
  favorite_categories: string[];
  preferred_difficulty?: 'easy' | 'medium' | 'hard';
  preferred_cost?: 'free' | 'low' | 'medium' | 'high';
  preferred_duration_min?: number;
  preferred_duration_max?: number;
  notification_preferences: Record<string, any>;
  privacy_settings: Record<string, any>;
  ui_preferences: Record<string, any>;
  created_at: string;
  updated_at: string;
}

class UserPreferencesService {
  /**
   * Get user preferences, creating default ones if they don't exist
   */
  async getUserPreferences(userId: string): Promise<UserPreferences | null> {
    try {
      const { data, error } = await supabase
        .rpc('get_user_preferences', { user_uuid: userId });

      if (error) {
        logger.error('Error getting user preferences:', error);
        throw new Error(`Failed to get user preferences: ${error.message}`);
      }

      return data;
    } catch (error) {
      logger.error('Error in getUserPreferences:', error);
      return null;
    }
  }

  /**
   * Hide a date night idea for the user
   */
  async hideDateNightIdea(userId: string, compositeId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .rpc('hide_date_night_idea', { 
          user_uuid: userId, 
          idea_composite_id: compositeId 
        });

      if (error) {
        logger.error('Error hiding date night idea:', error);
        throw new Error(`Failed to hide idea: ${error.message}`);
      }

      logger.info(`Date night idea ${compositeId} hidden for user ${userId}`);
      return data;
    } catch (error) {
      logger.error('Error in hideDateNightIdea:', error);
      throw error;
    }
  }

  /**
   * Unhide a date night idea for the user
   */
  async unhideDateNightIdea(userId: string, compositeId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .rpc('unhide_date_night_idea', { 
          user_uuid: userId, 
          idea_composite_id: compositeId 
        });

      if (error) {
        logger.error('Error unhiding date night idea:', error);
        throw new Error(`Failed to unhide idea: ${error.message}`);
      }

      logger.info(`Date night idea ${compositeId} unhidden for user ${userId}`);
      return data;
    } catch (error) {
      logger.error('Error in unhideDateNightIdea:', error);
      throw error;
    }
  }

  /**
   * Check if a date night idea is hidden for the user
   */
  async isIdeaHidden(userId: string, compositeId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .rpc('is_idea_hidden', { 
          user_uuid: userId, 
          idea_composite_id: compositeId 
        });

      if (error) {
        logger.error('Error checking if idea is hidden:', error);
        return false;
      }

      return data || false;
    } catch (error) {
      logger.error('Error in isIdeaHidden:', error);
      return false;
    }
  }

  /**
   * Get all hidden date night ideas for the user
   */
  async getHiddenIdeas(userId: string): Promise<string[]> {
    try {
      const preferences = await this.getUserPreferences(userId);
      return preferences?.hidden_date_night_ideas || [];
    } catch (error) {
      logger.error('Error getting hidden ideas:', error);
      return [];
    }
  }

  /**
   * Update user preferences
   */
  async updatePreferences(
    userId: string, 
    updates: Partial<Omit<UserPreferences, 'id' | 'user_id' | 'created_at' | 'updated_at'>>
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: userId,
          ...updates,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (error) {
        logger.error('Error updating user preferences:', error);
        throw new Error(`Failed to update preferences: ${error.message}`);
      }

      logger.info(`User preferences updated for user ${userId}`);
      return true;
    } catch (error) {
      logger.error('Error in updatePreferences:', error);
      throw error;
    }
  }

  /**
   * Set favorite categories for the user
   */
  async setFavoriteCategories(userId: string, categories: string[]): Promise<boolean> {
    return this.updatePreferences(userId, { favorite_categories: categories });
  }

  /**
   * Set preferred difficulty for the user
   */
  async setPreferredDifficulty(userId: string, difficulty: 'easy' | 'medium' | 'hard'): Promise<boolean> {
    return this.updatePreferences(userId, { preferred_difficulty: difficulty });
  }

  /**
   * Set preferred cost level for the user
   */
  async setPreferredCost(userId: string, cost: 'free' | 'low' | 'medium' | 'high'): Promise<boolean> {
    return this.updatePreferences(userId, { preferred_cost: cost });
  }

  /**
   * Set preferred duration range for the user
   */
  async setPreferredDuration(userId: string, minMinutes: number, maxMinutes: number): Promise<boolean> {
    return this.updatePreferences(userId, { 
      preferred_duration_min: minMinutes,
      preferred_duration_max: maxMinutes 
    });
  }

  /**
   * Filter out hidden ideas from a list of date night ideas
   */
  async filterHiddenIdeas<T extends { composite_id: string }>(
    userId: string, 
    ideas: T[]
  ): Promise<T[]> {
    try {
      const hiddenIds = await this.getHiddenIdeas(userId);
      return ideas.filter(idea => !hiddenIds.includes(idea.composite_id));
    } catch (error) {
      logger.error('Error filtering hidden ideas:', error);
      // Return original list if filtering fails
      return ideas;
    }
  }
}

export const userPreferencesService = new UserPreferencesService();
