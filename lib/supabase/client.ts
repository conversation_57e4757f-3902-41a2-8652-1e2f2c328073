import { Platform } from 'react-native';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '../../types/supabase';
import { logger } from '../../utils/logger';

// Only import storage adapter for native platforms
let createStorageAdapter: any;
if (Platform.OS !== 'web') {
  createStorageAdapter = require('./storage').createStorageAdapter;
}

// Supabase configuration - MUST be set via environment variables
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

// Validate configuration - fail fast if not properly configured
if (!supabaseUrl || !supabaseAnonKey) {
  const errorMessage = 'CRITICAL: Supabase configuration missing. Please set EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY environment variables.';
  logger.error(errorMessage);
  throw new Error(errorMessage);
}

logger.info('Supabase configuration validated successfully');

// Create Supabase client with platform-specific configuration
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    // For web, completely disable storage to avoid AsyncStorage issues
    // For native, use our custom storage adapter
    storage: Platform.OS === 'web' ? undefined : createStorageAdapter?.(),
    autoRefreshToken: Platform.OS !== 'web',
    persistSession: Platform.OS !== 'web',
    detectSessionInUrl: false,
    // Prevent initial session loading on web during SSR
    flowType: Platform.OS === 'web' ? 'implicit' : 'pkce',
  },
});

// Helper function to get current user
export const getCurrentUser = async () => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      logger.error('Error getting current user:', error);
      return null;
    }
    return user;
  } catch (error) {
    logger.error('Error getting current user:', error);
    return null;
  }
};

// Helper function to check if user is authenticated
export const isAuthenticated = async () => {
  const user = await getCurrentUser();
  return !!user;
};

// Helper function to sign out
export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) {
      logger.error('Error signing out:', error);
      throw error;
    }
    logger.info('User signed out successfully');
  } catch (error) {
    logger.error('Error signing out:', error);
    throw error;
  }
};

export default supabase;
