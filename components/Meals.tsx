import React, { useState, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  Animated,
  Dimensions,
} from 'react-native';

import { 
  Utensils, 
  Heart, 
  Clock, 
  Users, 
  Plus, 
  Search, 
  Filter,
  History,
  CheckCircle,
  XCircle,
  Sparkles
} from 'lucide-react-native';
import { colors } from '../utils/colors';
import { useMealIdeasSupabase } from '../hooks/useMealIdeasSupabase';
import { useUserProfile } from '../hooks/useUserProfile';
import { useAuth } from '../hooks/useAuth';
import { PointsDisplay } from './PointsDisplay';
import { SurpriseMe, SurpriseItem, HeartToggle, TopTabs } from './shared';
import { getRandomMealIdeas } from '../utils/surpriseHelpers';
import { favoritesService } from '../services/favoritesService';

const { width } = Dimensions.get('window');

// Types for meal voting (simplified from the original)
export interface MealOption {
  id: string;
  option: string;
  emoji?: string;
  addedBy: string;
  votes: { [partnerId: string]: 'yes' | 'no' };
}

export interface MealVoteRound {
  id: string;
  title: string;
  options: MealOption[];
  winner?: MealOption;
  createdAt: number;
  completedAt?: number;
  partner1Id: string;
  partner2Id: string;
}

type TabType = 'ideas' | 'voting' | 'history';

export default function Meals() {
  const [activeTab, setActiveTab] = useState<TabType>('ideas');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddMeal, setShowAddMeal] = useState(false);
  
  // Meal voting state
  const [currentRound, setCurrentRound] = useState<MealVoteRound | null>(null);
  const [newMealOption, setNewMealOption] = useState('');
  const [newMealEmoji, setNewMealEmoji] = useState('');
  
  const { profile, getPartnerNames } = useUserProfile();
  const { user } = useAuth();
  const { 
    combinedIdeas, 
    categories, 
    isLoading, 
    createUserMealIdea,
    markAsCompleted,
    searchMealIdeas,
    refreshData
  } = useMealIdeasSupabase();

  const partnerNames = getPartnerNames();

  // SurpriseMe handlers
  const handleSurpriseMeals = useCallback(async (): Promise<SurpriseItem[]> => {
    if (!user?.id) return [];
    return await getRandomMealIdeas(user.id, 3);
  }, [user?.id]);

  const handleMealSelect = useCallback((item: SurpriseItem) => {
    // Find the meal in combinedIdeas and show details
    const meal = combinedIdeas.find(idea => idea.id === item.id);
    if (meal) {
      Alert.alert(
        meal.title,
        `${meal.description || 'No description available'}\n\nCategory: ${meal.category}\nDifficulty: ${meal.difficulty}\nServings: ${meal.servings}${meal.prepTime ? `\nPrep Time: ${meal.prepTime}min` : ''}${meal.cookTime ? `\nCook Time: ${meal.cookTime}min` : ''}`,
        [{ text: 'OK' }]
      );
    }
  }, [combinedIdeas]);

  const handleMealSave = useCallback(async (item: SurpriseItem) => {
    if (!user?.id) return;
    
    try {
      await favoritesService.toggleFavorite(user.id, item.id, 'meal', true);
      Alert.alert('Saved!', `${item.title} has been added to your favorites!`);
    } catch (error) {
      console.error('Failed to save meal:', error);
      Alert.alert('Error', 'Failed to save meal. Please try again.');
    }
  }, [user?.id]);

  const handleMealPlan = useCallback((item: SurpriseItem) => {
    Alert.alert(
      'Plan This Meal! 🍽️',
      `Planning "${item.title}" for your next meal!\n\nThis feature will be available soon - for now, you can save it to your favorites!`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Save Instead', 
          onPress: () => handleMealSave(item)
        }
      ]
    );
  }, [handleMealSave]);

  // Filter meal ideas based on category and search
  const filteredMealIdeas = useMemo(() => {
    // Ensure combinedIdeas is always an array
    let filtered = Array.isArray(combinedIdeas) ? combinedIdeas : [];

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(idea => idea.category === selectedCategory);
    }

    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(idea => 
        idea.title.toLowerCase().includes(searchLower) ||
        idea.description?.toLowerCase().includes(searchLower) ||
        idea.ingredients.some(ingredient => ingredient.toLowerCase().includes(searchLower))
      );
    }

    return filtered;
  }, [combinedIdeas, selectedCategory, searchTerm]);

  // Create new meal voting round
  const createNewRound = () => {
    const newRound: MealVoteRound = {
      id: `round_${Date.now()}`,
      title: 'What Should We Eat?',
      options: [],
      partner1Id: partnerNames[0],
      partner2Id: partnerNames[1],
      createdAt: Date.now()
    };
    setCurrentRound(newRound);
    setActiveTab('voting');
  };

  // Add meal option to current round
  const addMealOption = () => {
    if (!newMealOption.trim() || !currentRound) return;

    const newOption: MealOption = {
      id: `option_${Date.now()}`,
      option: newMealOption.trim(),
      emoji: newMealEmoji || '🍽️',
      addedBy: partnerNames[0], // Default to partner 1
      votes: {}
    };

    setCurrentRound({
      ...currentRound,
      options: [...currentRound.options, newOption]
    });

    setNewMealOption('');
    setNewMealEmoji('');
  };

  // Vote on meal option
  const voteOnOption = (optionId: string, partnerId: string, vote: 'yes' | 'no') => {
    if (!currentRound) return;

    const updatedOptions = currentRound.options.map(option => {
      if (option.id === optionId) {
        return {
          ...option,
          votes: {
            ...option.votes,
            [partnerId]: vote
          }
        };
      }
      return option;
    });

    setCurrentRound({
      ...currentRound,
      options: updatedOptions
    });
  };

  // Complete voting round and determine winner
  const completeVotingRound = () => {
    if (!currentRound) return;

    // Calculate scores for each option
    const scoredOptions = currentRound.options.map(option => {
      const votes = Object.values(option.votes);
      const yesVotes = votes.filter(vote => vote === 'yes').length;
      const noVotes = votes.filter(vote => vote === 'no').length;
      const score = yesVotes - noVotes;
      
      return { ...option, score };
    });

    // Find winner (highest score)
    const winner = scoredOptions.reduce((prev, current) => 
      current.score > prev.score ? current : prev
    );

    const completedRound: MealVoteRound = {
      ...currentRound,
      winner,
      completedAt: Date.now()
    };

    // Save to history (you could implement this with Supabase)
    Alert.alert(
      'Winner Chosen! 🎉',
      `You've decided to have: ${winner.option} ${winner.emoji}`,
      [
        { text: 'Start New Round', onPress: createNewRound },
        { text: 'Done', onPress: () => setActiveTab('ideas') }
      ]
    );

    setCurrentRound(null);
  };

  // Add new meal idea
  const handleAddMealIdea = async () => {
    if (!newMealOption.trim()) {
      Alert.alert('Missing Information', 'Please enter a meal name.');
      return;
    }

    const success = await createUserMealIdea({
      title: newMealOption.trim(),
      description: '',
      category: 'Custom',
      emoji: newMealEmoji || '🍽️',
      difficulty: 'easy',
      servings: 2,
      ingredients: [],
      instructions: [],
      tags: ['custom'],
      source: 'user',
      isFavorite: false,
      isCompleted: false
    });

    if (success) {
      setNewMealOption('');
      setNewMealEmoji('');
      setShowAddMeal(false);
      Alert.alert('Success!', 'Meal idea added successfully!');
    } else {
      Alert.alert('Error', 'Failed to add meal idea. Please try again.');
    }
  };

  const renderIdeasTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Search and Filter */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Search size={20} color={colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search meal ideas..."
            value={searchTerm}
            onChangeText={setSearchTerm}
            placeholderTextColor={colors.textSecondary}
          />
        </View>
        
        <TouchableOpacity 
          style={styles.filterButton}
          onPress={() => setShowAddMeal(true)}
        >
          <Plus size={20} color={colors.white} />
        </TouchableOpacity>
      </View>

      {/* Surprise Me Component */}
      <SurpriseMe
        type="meal"
        onSurprise={handleSurpriseMeals}
        onItemSelect={handleMealSelect}
        onItemSave={handleMealSave}
        onItemPlan={handleMealPlan}
        title="Surprise Me with Meals!"
        subtitle="Get random meal suggestions"
        buttonText="🍽️ Surprise Me!"
        backgroundColor={colors.secondary}
        maxItems={3}
        disabled={!user?.id}
      />

      {/* Category Filter */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.categoryContainer}
      >
        <TouchableOpacity
          style={[styles.categoryChip, selectedCategory === 'all' && styles.categoryChipActive]}
          onPress={() => setSelectedCategory('all')}
        >
          <Text style={[styles.categoryChipText, selectedCategory === 'all' && styles.categoryChipTextActive]}>
            All
          </Text>
        </TouchableOpacity>
        
        {(categories || []).map(category => (
          <TouchableOpacity
            key={category}
            style={[styles.categoryChip, selectedCategory === category && styles.categoryChipActive]}
            onPress={() => setSelectedCategory(category)}
          >
            <Text style={[styles.categoryChipText, selectedCategory === category && styles.categoryChipTextActive]}>
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Meal Ideas Grid */}
      <View style={styles.ideasGrid}>
        {filteredMealIdeas.map(idea => (
          <TouchableOpacity key={idea.id} style={styles.ideaCard}>
            <View
              style={[styles.ideaCardGradient, { backgroundColor: colors.primary }]}
            >
              {/* Top section with heart and category */}
              <View style={styles.ideaCardTopSection}>
                <View style={styles.ideaCategoryContainer}>
                  <Text style={styles.ideaCategory}>{idea.category}</Text>
                </View>
                <HeartToggle
                  isFavorited={idea.isFavorite || false}
                  onToggle={async (newFavoritedState) => {
                    try {
                      if (!user?.id) throw new Error('User not authenticated');
                      await favoritesService.toggleFavorite(user.id, idea.id, 'meal', newFavoritedState);
                      console.log(`Meal idea ${idea.id} ${newFavoritedState ? 'favorited' : 'unfavorited'}`);
                    } catch (error) {
                      console.error('Failed to toggle meal favorite:', error);
                      Alert.alert('Error', 'Failed to update favorite. Please try again.');
                    }
                  }}
                  size={20}
                  style={styles.heartToggle}
                  accessibilityLabel={`${idea.isFavorite ? 'Remove from' : 'Add to'} favorites`}
                />
              </View>

              {/* Title and emoji */}
              <View style={styles.ideaTitleSection}>
                <Text style={styles.ideaEmoji}>{idea.emoji}</Text>
                <Text style={styles.ideaTitle}>{idea.title}</Text>
              </View>
              
              {idea.description && (
                <Text style={styles.ideaDescription} numberOfLines={2}>
                  {idea.description}
                </Text>
              )}
              
              <View style={styles.ideaMeta}>
                {idea.prepTime && (
                  <View style={styles.metaItem}>
                    <Clock size={12} color={colors.white} />
                    <Text style={styles.metaText}>{idea.prepTime}m</Text>
                  </View>
                )}
                <View style={styles.metaItem}>
                  <Users size={12} color={colors.white} />
                  <Text style={styles.metaText}>{idea.servings}</Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );

  const renderVotingTab = () => (
    <View style={styles.tabContent}>
      {!currentRound ? (
        <View style={styles.emptyState}>
          <Utensils size={48} color={colors.textTertiary} />
          <Text style={styles.emptyStateText}>No active voting round</Text>
          <Text style={styles.emptyStateSubtext}>Start a new round to decide what to eat!</Text>
          <TouchableOpacity style={styles.startButton} onPress={createNewRound}>
            <Text style={styles.startButtonText}>Start New Round</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Add New Option */}
          <View style={styles.addOptionContainer}>
            <Text style={styles.sectionTitle}>Add Meal Options</Text>
            <View style={styles.addOptionInput}>
              <TextInput
                style={styles.optionInput}
                placeholder="Enter meal option..."
                value={newMealOption}
                onChangeText={setNewMealOption}
                placeholderTextColor={colors.textSecondary}
              />
              <TextInput
                style={styles.emojiInput}
                placeholder="🍽️"
                value={newMealEmoji}
                onChangeText={setNewMealEmoji}
                placeholderTextColor={colors.textSecondary}
                maxLength={2}
              />
              <TouchableOpacity style={styles.addButton} onPress={addMealOption}>
                <Plus size={20} color={colors.white} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Voting Options */}
          <View style={styles.votingContainer}>
            <Text style={styles.sectionTitle}>Vote on Options</Text>
            {currentRound.options.map(option => (
              <View key={option.id} style={styles.votingOption}>
                <View style={styles.optionInfo}>
                  <Text style={styles.optionEmoji}>{option.emoji}</Text>
                  <Text style={styles.optionText}>{option.option}</Text>
                </View>
                
                <View style={styles.votingButtons}>
                  <TouchableOpacity
                    style={[styles.voteButton, styles.yesButton]}
                    onPress={() => voteOnOption(option.id, partnerNames[0], 'yes')}
                  >
                    <CheckCircle size={20} color={colors.white} />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.voteButton, styles.noButton]}
                    onPress={() => voteOnOption(option.id, partnerNames[0], 'no')}
                  >
                    <XCircle size={20} color={colors.white} />
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>

          {/* Complete Round Button */}
          {currentRound.options.length > 0 && (
            <TouchableOpacity style={styles.completeButton} onPress={completeVotingRound}>
              <Text style={styles.completeButtonText}>Complete Voting</Text>
              <Sparkles size={20} color={colors.white} />
            </TouchableOpacity>
          )}
        </ScrollView>
      )}
    </View>
  );

  const renderHistoryTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.emptyState}>
        <History size={48} color={colors.textTertiary} />
        <Text style={styles.emptyStateText}>No voting history yet</Text>
        <Text style={styles.emptyStateSubtext}>Complete some voting rounds to see your history here!</Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View
        style={[styles.header, { backgroundColor: colors.primary }]}
      >
        <Text style={styles.headerTitle}>🍽️ Meals</Text>
        <Text style={styles.headerSubtitle}>Discover, vote, and plan your meals together</Text>
      </View>

      {/* Tab Navigation */}
      <TopTabs
        backgroundColor={colors.secondary}
        containerStyle={{ marginHorizontal: 20, marginTop: 20 }}
        tabs={[
          { key: 'ideas', label: 'Ideas', icon: <Utensils size={20} /> },
          { key: 'voting', label: 'Voting', icon: <Heart size={20} /> },
          { key: 'history', label: 'History', icon: <History size={20} /> },
        ]}
        activeKey={activeTab}
        onChange={(key) => setActiveTab(key as TabType)}
      />

      {/* Tab Content */}
      {activeTab === 'ideas' && renderIdeasTab()}
      {activeTab === 'voting' && renderVotingTab()}
      {activeTab === 'history' && renderHistoryTab()}

      {/* Add Meal Modal */}
      {showAddMeal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Add New Meal Idea</Text>
            
            <TextInput
              style={styles.modalInput}
              placeholder="Meal name..."
              value={newMealOption}
              onChangeText={setNewMealOption}
              placeholderTextColor={colors.textSecondary}
            />
            
            <TextInput
              style={styles.modalInput}
              placeholder="Emoji (optional)"
              value={newMealEmoji}
              onChangeText={setNewMealEmoji}
              placeholderTextColor={colors.textSecondary}
              maxLength={2}
            />
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowAddMeal(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton]}
                onPress={handleAddMealIdea}
              >
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  // TopTabs used; remove local tab header styles to avoid duplication
  tabContent: {
    flex: 1,
    padding: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: colors.textPrimary,
  },
  filterButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoryContainer: {
    marginBottom: 20,
  },
  categoryChip: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
  },
  categoryChipActive: {
    backgroundColor: colors.primary,
  },
  categoryChipText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  categoryChipTextActive: {
    color: colors.white,
  },
  ideasGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  ideaCard: {
    width: (width - 52) / 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  ideaCardGradient: {
    padding: 16,
    minHeight: 140,
    position: 'relative',
  },
  ideaCardTopSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  ideaCategoryContainer: {
    flex: 1,
  },
  ideaCategory: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.white,
    opacity: 0.9,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  heartToggle: {
    position: 'relative',
    top: 0,
    right: 0,
    zIndex: 1,
    padding: 2,
  },
  ideaTitleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  ideaEmoji: {
    fontSize: 24,
  },
  ideaTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
    flex: 1,
  },
  ideaDescription: {
    fontSize: 12,
    color: colors.white,
    opacity: 0.9,
    lineHeight: 16,
    marginBottom: 12,
  },
  ideaMeta: {
    flexDirection: 'row',
    gap: 12,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    color: colors.white,
    opacity: 0.8,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  startButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  startButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  addOptionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 12,
  },
  addOptionInput: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  optionInput: {
    flex: 1,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textPrimary,
  },
  emojiInput: {
    width: 50,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textPrimary,
    textAlign: 'center',
  },
  addButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  votingContainer: {
    marginBottom: 24,
  },
  votingOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  optionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  optionEmoji: {
    fontSize: 24,
  },
  optionText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.textPrimary,
    flex: 1,
  },
  votingButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  voteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  yesButton: {
    backgroundColor: colors.success,
  },
  noButton: {
    backgroundColor: colors.error,
  },
  completeButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  completeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: colors.background,
    borderRadius: 16,
    padding: 24,
    width: width - 40,
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  modalInput: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textPrimary,
    marginBottom: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: colors.backgroundSecondary,
  },
  saveButton: {
    backgroundColor: colors.primary,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
