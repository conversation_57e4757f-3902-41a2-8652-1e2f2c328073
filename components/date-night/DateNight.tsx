import React, { useState, useMemo, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,

  Alert,
  Modal,
  ActivityIndicator,
  Dimensions
} from 'react-native';

import {
  Heart,
  Plus,
  Shuffle,
  CheckCircle,
  Calendar,
  Star,
  Trash2,
  X,
  EyeOff
} from 'lucide-react-native';
import { HeartToggle } from '../shared';

import { favoritesService } from '../../services/favoritesService';
import { dateNightIdeasService } from '../../services/dateNightIdeasService';

import { useAuth } from '../../hooks/useAuth';
import { useUserPreferences } from '../../hooks/useUserPreferences';
import { colors } from '../../utils/colors';
import Meals from '../Meals';
import {
  SearchBar,
  EmptyState,
  StatCard,
  CommonModal,
  StyledInput
} from '../shared/CommonComponents';
import { SurpriseItem, TopTabs, DSButton } from '../shared';

const { width } = Dimensions.get('window');

// Always render all 12 weeks
const WEEKS = Array.from({ length: 12 }, (_, i) => i + 1);

type TabType = 'discover' | 'my-ideas' | 'meals';

export default function DateNight() {
  // State for date night ideas with composite ID support
  const [allIdeas, setAllIdeas] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [favoriteStates, setFavoriteStates] = useState<Record<string, boolean>>({});
  const [favoriteLoadingStates, setFavoriteLoadingStates] = useState<Record<string, boolean>>({});

  const { user } = useAuth();
  const { hideIdea, isIdeaHidden, filterHiddenIdeas } = useUserPreferences();

  // Load date night ideas with composite IDs
  const loadIdeas = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const ideas = await dateNightIdeasService.getAllIdeas(user?.id);
      // Filter out hidden ideas
      const visibleIdeas = user?.id ? filterHiddenIdeas(ideas) : ideas;
      setAllIdeas(visibleIdeas);

      // Load favorite states for all ideas
      if (user?.id && ideas.length > 0) {
        try {
          const favorites = await favoritesService.getUserFavorites(user.id, 'date_night');
          const favoriteIds = new Set(favorites.map(f => f.item_id));
          const newFavoriteStates: Record<string, boolean> = {};
          ideas.forEach(idea => {
            newFavoriteStates[idea.composite_id] = favoriteIds.has(idea.composite_id);
          });
          setFavoriteStates(newFavoriteStates);
        } catch (e) {
          console.warn('Failed to load favorites; defaulting to empty', e);
          setFavoriteStates({});
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load date night ideas');
      console.error('Error loading date night ideas:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  // Load ideas on mount
  useEffect(() => {
    loadIdeas();
  }, [loadIdeas]);

  // Expose a simple refresh handler for retry button
  const refreshData = useCallback(() => {
    loadIdeas();
  }, [loadIdeas]);

  // Toggle favorite function with composite ID support
  const toggleFavorite = useCallback(async (compositeId: string, metadata?: Record<string, any>) => {
    if (!user?.id) {
      Alert.alert('Error', 'Please log in to favorite ideas');
      return;
    }

    try {
      setFavoriteLoadingStates(prev => ({ ...prev, [compositeId]: true }));

      const currentlyFavorited = favoriteStates[compositeId] || false;
      const newFavoriteState = !currentlyFavorited;

      // Update local state immediately for better UX
      setFavoriteStates(prev => ({ ...prev, [compositeId]: newFavoriteState }));

      // Update in database
      await favoritesService.toggleFavorite(user.id, compositeId, 'date_night', newFavoriteState, metadata);

      console.log(`Date night idea ${compositeId} ${newFavoriteState ? 'favorited' : 'unfavorited'}`);
    } catch (error) {
      // Revert local state on error
      setFavoriteStates(prev => ({ ...prev, [compositeId]: !favoriteStates[compositeId] }));
      console.error('Failed to toggle date night favorite:', error);
      Alert.alert('Error', 'Failed to update favorite. Please try again.');
    } finally {
      setFavoriteLoadingStates(prev => ({ ...prev, [compositeId]: false }));
    }
  }, [user?.id, favoriteStates]);

  // Check if an idea is favorited
  const isFavorited = useCallback((compositeId: string): boolean => {
    return favoriteStates[compositeId] || false;
  }, [favoriteStates]);

  // SurpriseMe handlers
  const handleSurpriseDateNights = useCallback(async () => {
    try {
      // Get 3 random ideas from all available ideas
      const shuffled = [...(allIdeas || [])].sort(() => 0.5 - Math.random());
      const randomIdeas = shuffled.slice(0, 3);

      // Convert to SurpriseItem format
      const surpriseItems = randomIdeas.map(idea => ({
        id: idea.composite_id,
        title: idea.title,
        description: idea.description,
        emoji: idea.emoji || '💕',
        category: idea.category || 'Date Night'
      }));

      console.log(`Selected ${surpriseItems.length} random date night ideas from ${allIdeas?.length || 0} total available`);
      setSurpriseIdeas(surpriseItems);
      setShowSurpriseModal(true);
    } catch (error) {
      console.error('Error getting surprise date night ideas:', error);
      Alert.alert('Error', 'Failed to get surprise date night ideas. Please try again.');
    }
  }, [allIdeas]);


  const handleDateNightSelect = useCallback((item: SurpriseItem) => {
    // Find the date night idea and show details
    const idea = allIdeas.find(i => i.id === item.id);
    if (idea) {
      Alert.alert(
        idea.title,
        `${idea.description}\n\nCategory: ${idea.category || 'N/A'}\nDifficulty: ${idea.difficulty || 'N/A'}\nDuration: ${idea.estimatedDuration || 'N/A'} minutes\nCost: ${idea.costLevel || 'N/A'}\nLocation: ${idea.indoorOutdoor || 'N/A'}`,
        [{ text: 'OK' }]
      );
    }
  }, [allIdeas]);

  const handleDateNightSave = useCallback(async (item: SurpriseItem) => {
    try {
      // Find the idea by ID and use composite ID for favoriting
      const idea = allIdeas?.find(i => i.id === item.id || i.composite_id === item.id);
      if (idea) {
        await toggleFavorite(idea.composite_id, {
          user_action: 'surprise_save',
          source: 'surprise_modal',
          timestamp: Date.now(),
        });
        Alert.alert('Saved!', `${item.title} has been added to your favorites!`);
      } else {
        Alert.alert('Error', 'Could not find the date night idea.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to save date night idea. Please try again.');
    }
  }, [allIdeas, toggleFavorite]);

  // State management
  const [activeTab, setActiveTab] = useState<TabType>('discover');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showSurpriseModal, setShowSurpriseModal] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'weekly' | 'favorites' | 'completed'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [randomIdeas, setRandomIdeas] = useState<any[]>([]);
  const [surpriseIdeas, setSurpriseIdeas] = useState<SurpriseItem[]>([]);

  const [newDateNight, setNewDateNight] = useState({
    title: '',
    description: '',
    emoji: '',
    category: 'Custom'
  });

  // Computed values
  const totalIdeas = allIdeas.length;
  const favoriteIdeas = allIdeas.filter(idea => favoriteStates[idea.composite_id]);
  const favoriteCount = favoriteIdeas.length;

  const filteredIdeas = useMemo(() => {
    let filtered = allIdeas || [];

    // Apply source filter
    if (selectedFilter === 'weekly') {
      // For now, show all ideas - we can add week filtering later
      filtered = allIdeas || [];
    } else if (selectedFilter === 'favorites') {
      filtered = favoriteIdeas;
    } else if (selectedFilter === 'completed') {
      // For now, show empty - we can add completed status later
      filtered = [];
    }

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(idea =>
        idea.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        idea.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        idea.category?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filtered;
  }, [allIdeas, favoriteIdeas, selectedFilter, searchQuery]);

  // Group ideas by week number
  const ideasByWeek = useMemo(() => {
    const grouped: { [week: number]: any[] } = {};
    
    // Initialize all weeks with empty arrays
    WEEKS.forEach(week => {
      grouped[week] = [];
    });
    
    // Group filtered ideas by week
    (filteredIdeas || []).forEach(idea => {
      const week = idea.weekNumber || 1; // Default to week 1 if no week specified
      if (week >= 1 && week <= 12) {
        grouped[week].push(idea);
      }
    });
    
    // Sort ideas within each week by title
    WEEKS.forEach(week => {
      grouped[week].sort((a, b) => a.title.localeCompare(b.title));
    });
    
    return grouped;
  }, [filteredIdeas]);

  // Event handlers
  const handleAddDateNight = useCallback(async () => {
    if (!newDateNight.title.trim() || !newDateNight.description.trim()) {
      Alert.alert('Missing Information', 'Please provide both a title and description for your Date Night idea.');
      return;
    }

    Alert.alert('Custom Ideas', 'Custom date night ideas will be available soon! For now, you can save existing ideas to your favorites.');
    setNewDateNight({ title: '', description: '', emoji: '', category: 'Custom' });
    setShowAddModal(false);
  }, [newDateNight]);

  const handleRandomize = useCallback(async () => {
    try {
      // Get 3 random ideas from all available ideas
      const shuffled = [...(allIdeas || [])].sort(() => 0.5 - Math.random());
      const random = shuffled.slice(0, 3);
      setRandomIdeas(random);
    } catch (error) {
      Alert.alert('Error', 'Failed to get random ideas. Please try again.');
    }
  }, [allIdeas]);

  const handleSaveIdea = useCallback(async (ideaId: string, status: 'favorite' | 'planned' | 'completed') => {
    try {
      // Find the idea by ID and use composite ID for favoriting
      const idea = allIdeas?.find(i => i.id === ideaId || i.composite_id === ideaId);
      if (idea && status === 'favorite') {
        await toggleFavorite(idea.composite_id, {
          user_action: 'save_idea',
          source: 'date_night_page',
          timestamp: Date.now(),
        });
        Alert.alert('Saved!', `Idea has been saved to your ${status} list!`);
      } else {
        Alert.alert('Info', `${status === 'planned' ? 'Planning' : 'Completion'} features coming soon!`);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to save idea. Please try again.');
    }
  }, [allIdeas, toggleFavorite]);

  const handleRemoveIdea = useCallback(async (ideaId: string) => {
    Alert.alert(
      'Remove Favorite',
      'Are you sure you want to remove this idea from your favorites?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              // Find the idea by ID and use composite ID for unfavoriting
              const idea = allIdeas?.find(i => i.id === ideaId || i.composite_id === ideaId);
              if (idea) {
                await toggleFavorite(idea.composite_id, {
                  user_action: 'remove_favorite',
                  source: 'date_night_page',
                  timestamp: Date.now(),
                });
                Alert.alert('Removed', 'Idea has been removed from your favorites.');
              } else {
                Alert.alert('Error', 'Could not find the date night idea.');
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to remove idea. Please try again.');
            }
          }
        }
      ]
    );
  }, [allIdeas, toggleFavorite]);

  const handleHideIdea = useCallback(async (compositeId: string, title: string) => {
    try {
      Alert.alert(
        'Hide Idea',
        `Are you sure you want to hide "${title}"? You can unhide it later in your preferences.`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Hide',
            style: 'destructive',
            onPress: async () => {
              try {
                await hideIdea(compositeId);
                // Reload ideas to reflect the change
                await loadIdeas();
                Alert.alert('Hidden', 'Idea has been hidden from your feed.');
              } catch (error) {
                console.error('Failed to hide idea:', error);
                Alert.alert('Error', 'Failed to hide idea. Please try again.');
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error in handleHideIdea:', error);
    }
  }, [hideIdea, loadIdeas]);

  const handlePlanDateNight = useCallback((idea: any) => {
    const currentDate = new Date();
    const suggestedDate = new Date(currentDate.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    Alert.alert(
      'Plan Date Night ✨',
      `Planning "${idea.title}" for ${suggestedDate.toLocaleDateString()}!`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Plan It!', 
          onPress: () => {
            handleSaveIdea(idea.id, 'planned');
            Alert.alert(
              'Date Night Planned! 📅',
              `"${idea.title}" has been added to your planned date nights!\n\nYou'll both receive reminders as the date approaches!`,
              [{ text: 'Perfect! 💕' }]
            );
          }
        }
      ]
    );
  }, [handleSaveIdea]);

  const handleDateNightPlan = useCallback((item: SurpriseItem) => {
    const idea = allIdeas.find(i => i.id === item.id);
    if (idea) {
      handlePlanDateNight(idea);
    }
  }, [allIdeas, handlePlanDateNight]);

  // Render functions


  const renderIdeaCard = (idea: any) => {
    const isIdeaFavorited = isFavorited(idea.composite_id);
    const isLoading = favoriteLoadingStates[idea.composite_id];

    // Determine card color based on idea source
    const isSystemIdea = idea.source === 'global' || idea.source === 'weekly' || idea.id?.startsWith('demo-');
    const cardBackgroundColor = isSystemIdea ? colors.greenSage : colors.darkerPink;

    return (
      <TouchableOpacity key={idea.id} style={styles.ideaCard} activeOpacity={0.8}>
        <View style={[styles.cardSurface, { backgroundColor: cardBackgroundColor }]} >
          {/* Top section with heart, hide button, and category */}
          <View style={styles.cardTopSection}>
            <View style={styles.categoryContainer}>
              {idea.category && (
                <Text style={styles.categoryText}>{idea.category}</Text>
              )}
            </View>
            <View style={styles.cardActions}>
              <TouchableOpacity
                onPress={() => handleHideIdea(idea.composite_id, idea.title)}
                style={styles.hideButton}
                disabled={isLoading}
                accessibilityLabel="Hide this idea"
              >
                <EyeOff size={18} color={colors.white} />
              </TouchableOpacity>
              <HeartToggle
                isFavorited={isIdeaFavorited}
                onToggle={async (newFavoritedState) => {
                  try {
                    await toggleFavorite(idea.composite_id, {
                      user_action: 'manual_toggle',
                      source: 'date_night_page',
                      timestamp: Date.now(),
                    });
                    console.log(`Date night idea ${idea.composite_id} ${newFavoritedState ? 'favorited' : 'unfavorited'}`);
                  } catch (error) {
                    console.error('Failed to toggle date night favorite:', error);
                    Alert.alert('Error', 'Failed to update favorite. Please try again.');
                  }
                }}
                size={20}
                style={styles.heartToggle}
                disabled={isLoading}
                accessibilityLabel={`${isIdeaFavorited ? 'Remove from' : 'Add to'} favorites`}
              />
            </View>
          </View>

          {/* Title and emoji */}
          <View style={styles.cardTitleSection}>
            {idea.emoji && <Text style={styles.emoji}>{idea.emoji}</Text>}
            <Text style={styles.cardTitle}>{idea.title}</Text>
          </View>

          {/* Description */}
          <Text style={styles.cardDescription} numberOfLines={4} ellipsizeMode="tail">
            {idea.description}
          </Text>

          {/* Remove favorite button for favorited items */}
          {isIdeaFavorited && (
            <TouchableOpacity
              onPress={() => handleRemoveIdea(idea.composite_id)}
              style={styles.deleteButton}
              disabled={isLoading}
            >
              <Trash2 size={16} color={colors.white} />
            </TouchableOpacity>
          )}

          {/* Completed indicator - feature coming soon */}
          {false && (
            <View style={styles.completedInfo}>
              <CheckCircle size={16} color={colors.success} />
              <Text style={styles.completedText}>Completed!</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return colors.success;
      case 'medium': return colors.warning;
      case 'hard': return colors.error;
      default: return colors.primary;
    }
  };

  const renderDiscoverTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <View style={styles.headerSection}>
        <Text style={styles.tabTitle}>Discover Date Nights ✨</Text>
        <Text style={styles.tabSubtitle}>Find your next perfect evening together</Text>
      </View>

      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder="Search date night ideas..."
        onFilterPress={() => setShowFilterModal(true)}
        showFilter={true}
      />

      <View style={styles.quickActions}>
        <View style={{ width: '48%' }}>
          <DSButton title="Surprise Us!" onPress={handleSurpriseDateNights} tone="lavender" leftIcon={<Shuffle size={20} color={colors.white} />} />
        </View>
        <View style={{ width: '48%' }}>
          <DSButton title="Add Idea" onPress={() => setShowAddModal(true)} tone="sage" leftIcon={<Plus size={20} color={colors.white} />} />
        </View>
      </View>

      {(randomIdeas?.length || 0) > 0 && (
        <View style={styles.randomIdeasContainer}>
          <View style={styles.randomIdeasHeader}>
            <Text style={styles.randomIdeasTitle}>🎲 Random Picks</Text>
            <TouchableOpacity onPress={() => setRandomIdeas([])}>
              <X size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
          {randomIdeas?.map(renderIdeaCard) || []}
        </View>
      )}

      {/* Render all 12 weeks */}
      {WEEKS.map(week => {
        const weekIdeas = ideasByWeek[week] || [];

        return (
          <View key={week} style={styles.section}>
            <View style={styles.ideasGrid}>
              {weekIdeas.map(renderIdeaCard)}
            </View>
          </View>
        );
      })}
    </ScrollView>
  );

  const renderMyIdeasTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <View style={styles.headerSection}>
        <Text style={styles.tabTitle}>My Date Nights 💕</Text>
        <Text style={styles.tabSubtitle}>Your saved and planned date nights</Text>
      </View>

      <View style={styles.statsContainer}>
        <StatCard
          number={totalIdeas}
          label="Total Ideas"
          icon={<Calendar size={20} color={colors.white} />}
          color={colors.primary}
        />
        <StatCard
          number={favoriteCount}
          label="Favorites"
          icon={<Heart size={20} color={colors.white} />}
          color={colors.error}
        />
        <StatCard
          number={0}
          label="Completed"
          icon={<CheckCircle size={20} color={colors.white} />}
          color={colors.success}
        />
      </View>

      {(favoriteIdeas.length || 0) === 0 ? (
        <EmptyState
          icon={<Star size={48} color={colors.textTertiary} />}
          title="No saved date night ideas yet"
          subtitle="Start exploring and saving ideas you love!"
        />
      ) : (
        favoriteIdeas.map(idea => renderIdeaCard(idea))
      )}
    </ScrollView>
  );

  const renderMealsTab = () => (
    <Meals />
  );


  const renderCurrentTab = () => {
    switch (activeTab) {
      case 'discover':
        return renderDiscoverTab();
      case 'my-ideas':
        return renderMyIdeasTab();
      case 'meals':
        return renderMealsTab();
      default:
        return renderDiscoverTab();
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading date night ideas...</Text>
        </View>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Error loading date night ideas</Text>
          <View style={{ alignSelf: 'stretch' }}>
            <DSButton title="Retry" onPress={refreshData} tone="sage" />
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        <TopTabs
          backgroundColor={colors.secondary}
          tabs={[
            { key: 'discover', label: 'Discover', icon: '✨' },
            { key: 'my-ideas', label: 'Our Ideas', icon: '💕' },
            { key: 'meals', label: 'Meals', icon: '🍽️' },
          ]}
          activeKey={activeTab}
          onChange={(key) => setActiveTab(key as TabType)}
        />
      </View>

      <View style={styles.contentContainer}>
        {renderCurrentTab()}
      </View>

      <CommonModal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add Date Night Idea"
      >
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Title *</Text>
          <StyledInput
            value={newDateNight.title}
            onChangeText={(text) => setNewDateNight(prev => ({ ...prev, title: text }))}
            placeholder="e.g., Coffee Shop Crawl"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Description *</Text>
          <StyledInput
            value={newDateNight.description}
            onChangeText={(text) => setNewDateNight(prev => ({ ...prev, description: text }))}
            placeholder="Describe your date night idea..."
            multiline
            numberOfLines={4}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Emoji (Optional)</Text>
          <StyledInput
            value={newDateNight.emoji}
            onChangeText={(text) => setNewDateNight(prev => ({ ...prev, emoji: text }))}
            placeholder="🎭 🎨 🍕"
          />
        </View>

        <View style={styles.modalFooter}>
          <View style={{ flex: 1 }}>
            <DSButton title="Cancel" onPress={() => setShowAddModal(false)} variant="outline" tone="lavender" />
          </View>
          <View style={{ flex: 1 }}>
            <DSButton title="Add Idea" onPress={handleAddDateNight} variant="primary" tone="sage" />
          </View>
        </View>
      </CommonModal>

      <Modal
        visible={showFilterModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowFilterModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter Date Nights</Text>
              <TouchableOpacity onPress={() => setShowFilterModal(false)}>
                <X size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              {[
                { key: 'all', label: 'All Ideas', count: totalIdeas },
                { key: 'weekly', label: 'Weekly Challenges', count: totalIdeas },
                { key: 'favorites', label: 'Your Favorites', count: favoriteCount },
                { key: 'completed', label: 'Completed', count: 0 }
              ].map((filter) => (
                <TouchableOpacity
                  key={filter.key}
                  style={[
                    styles.filterOption,
                    selectedFilter === filter.key && styles.filterOptionSelected
                  ]}
                  onPress={() => {
                    setSelectedFilter(filter.key as any);
                    setShowFilterModal(false);
                  }}
                >
                  <Text style={[
                    styles.filterOptionText,
                    selectedFilter === filter.key && styles.filterOptionTextSelected
                  ]}>
                    {filter.label}
                  </Text>
                  <View style={styles.filterOptionCount}>
                    <Text style={[
                      styles.filterOptionCountText,
                      selectedFilter === filter.key && styles.filterOptionCountTextSelected
                    ]}>
                      {filter.count}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </Modal>

      {/* Surprise Modal */}
      <Modal
        visible={showSurpriseModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowSurpriseModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>🎲 Your Surprise Date Nights!</Text>
              <TouchableOpacity onPress={() => setShowSurpriseModal(false)}>
                <X size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody}>
              {surpriseIdeas?.map((item) => {
                const idea = allIdeas?.find(i => i.composite_id === item.id || i.id === item.id);
                if (!idea) return null;

                const isIdeaFavorited = isFavorited(idea.composite_id);
                const isLoading = favoriteLoadingStates[idea.composite_id];
                
                return (
                  <View key={item.id} style={styles.surpriseIdeaCard}>
                    <View
                      style={[styles.surpriseCardSurface, { backgroundColor: colors.primary }]}
                    >
                      {/* Top section with heart and category */}
                      <View style={styles.surpriseCardTopSection}>
                        <View style={styles.surpriseCategoryContainer}>
                          {idea.category && (
                            <Text style={styles.surpriseCategoryText}>{idea.category}</Text>
                          )}
                        </View>
                        <HeartToggle
                          isFavorited={isIdeaFavorited}
                          onToggle={async (newFavoritedState) => {
                            try {
                              await toggleFavorite(idea.composite_id, {
                                user_action: 'manual_toggle',
                                source: 'surprise_modal',
                                timestamp: Date.now(),
                              });
                              console.log(`Surprise date night idea ${idea.composite_id} ${newFavoritedState ? 'favorited' : 'unfavorited'}`);
                            } catch (error) {
                              console.error('Failed to toggle surprise date night favorite:', error);
                              Alert.alert('Error', 'Failed to update favorite. Please try again.');
                            }
                          }}
                          size={20}
                          style={styles.surpriseHeartToggle}
                          disabled={isLoading}
                          accessibilityLabel={`${isIdeaFavorited ? 'Remove from' : 'Add to'} favorites`}
                        />
                      </View>

                      {/* Title and emoji */}
                      <View style={styles.surpriseCardTitleSection}>
                        {idea.emoji && <Text style={styles.surpriseEmoji}>{idea.emoji}</Text>}
                        <Text style={styles.surpriseCardTitle}>{idea.title}</Text>
                      </View>

                      {/* Description */}
                      <Text style={styles.surpriseCardDescription}>{idea.description}</Text>
                    </View>
                  </View>
                );
              })}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundPrimary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 18,
    color: colors.textSecondary,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: colors.error,
    marginBottom: 16,
  },

  tabContainer: {
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  // TopTabs uses shared styles; local tab styles removed to avoid duplication
  contentContainer: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
    padding: 20,
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  tabTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
    textAlign: 'center',
  },
  tabSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  searchFilterBar: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 12,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textPrimary,
  },
  filterButton: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  quickActions: {
    flexDirection: 'row',
    marginBottom: 20,
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: '48%', // Same width as idea cards
    borderRadius: 12,
    overflow: 'hidden',
  },
  quickActionButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    gap: 8,
  },
  quickActionButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  randomizeButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  randomizeButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    gap: 8,
  },
  randomizeButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  addButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  addButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    gap: 8,
  },
  addButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  randomIdeasContainer: {
    marginBottom: 20,
  },
  randomIdeasHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  randomIdeasTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
  },
  ideasGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: colors.textTertiary,
    textAlign: 'center',
  },
  ideaCard: {
    width: '48%', // Ensures 2 columns with gap
    height: 160, // Smaller fixed height for more compact cards
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    borderWidth: 0.5,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  cardSurface: {
    flex: 1,
    padding: 12,
    position: 'relative',
    justifyContent: 'space-between',
  },
  cardTopSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryContainer: {
    flex: 1,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.white,
    opacity: 0.9,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  hideButton: {
    padding: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  heartToggle: {
    position: 'relative',
    top: 0,
    right: 0,
    zIndex: 1,
    padding: 2,
  },
  cardTitleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 6,
  },
  emoji: {
    fontSize: 20,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
    flex: 1,
  },
  deleteButton: {
    padding: 4,
  },
  cardDescription: {
    fontSize: 14,
    color: colors.white,
    lineHeight: 20,
    marginBottom: 12,
    flex: 1,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  sourceIndicator: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  sourceText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.primary,
  },
  difficultyIndicator: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  difficultyText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.primary,
  },
  savedButton: {
    backgroundColor: colors.success,
  },
  completedInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 12,
  },
  completedText: {
    fontSize: 14,
    color: colors.white,
  },
  statsContainer: {
    flexDirection: 'row',
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  surpriseSection: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  surpriseButton: {
    backgroundColor: colors.accent3,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    marginBottom: 12,
  },
  surpriseButtonText: {
    color: colors.white,
    fontSize: 18,
    fontWeight: '600',
  },
  surpriseSubtext: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  horizontalScroll: {
    flexDirection: 'row',
  },
  horizontalCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    minWidth: 120,
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  horizontalEmoji: {
    fontSize: 24,
    marginBottom: 8,
  },
  horizontalTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textPrimary,
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 18,
  },
  smallButton: {
    backgroundColor: colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  smallButtonText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
  comingSoonText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
    padding: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.background,
    borderRadius: 20,
    width: width - 40,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  modalBody: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textPrimary,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  textArea: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textPrimary,
    borderWidth: 1,
    borderColor: colors.borderLight,
    textAlignVertical: 'top',
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: colors.backgroundSecondary,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  saveButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  saveButtonGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  saveButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  filterOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginBottom: 8,
  },
  filterOptionSelected: {
    backgroundColor: colors.primary,
  },
  filterOptionText: {
    fontSize: 16,
    color: colors.textPrimary,
  },
  filterOptionTextSelected: {
    color: colors.white,
  },
  filterOptionCount: {
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  filterOptionCountText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  filterOptionCountTextSelected: {
    color: colors.white,
  },
  // Surprise modal styles
  surpriseIdeaCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  surpriseCardSurface: {
    padding: 16,
    position: 'relative',
  },
  surpriseCardTopSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  surpriseCategoryContainer: {
    flex: 1,
  },
  surpriseCategoryText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.white,
    opacity: 0.9,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  surpriseHeartToggle: {
    position: 'relative',
    top: 0,
    right: 0,
    zIndex: 1,
    padding: 2,
  },
  surpriseCardTitleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  surpriseEmoji: {
    fontSize: 24,
  },
  surpriseCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.white,
    flex: 1,
  },
  surpriseCardDescription: {
    fontSize: 14,
    color: colors.white,
    lineHeight: 20,
    marginBottom: 12,
  },
});
