/**
 * Shared Components Index
 * 
 * Centralized export for all shared UI components.
 * This provides a single import point for all reusable components.
 * 
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

// Enhanced Components
export {
  GradientCard,
  StatCard,
  GradientButton,
  ProgressBar,
  WeekSelector,
  EnhancedModal,
  EnhancedInput,
  ActivityCard,
  StepNavigation,
} from './EnhancedComponents';

// Layout Components
export {
  ScreenLayout,
  HeaderLayout,
  ContentLayout,
  CardGrid,
  SectionLayout,
  FlexRow,
  FlexColumn,
  Spacer,
  Divider,
} from './LayoutComponents';

// Design System Templates (DS-prefixed for clarity)
export {
  HeaderBar as DSHeaderBar,
  Card as DSCard,
  StatTile as DSStatTile,
  Button as DSButton,
  Input as DSInput,
  GlassCard as DSGlassCard,
  Avatar as DSAvatar,
  ListItem as DSListItem,
  Badge as DSBadge,
  OverlayCard as DSOverlayCard,
  SectionHeader as DSSectionHeader,
  PillGroup as DSPillGroup,
  ProgressBar as DSProgressBar,
} from '../design-system/Templates';

// Design System Types
export type {
  HeaderBarProps as DSHeaderBarProps,
  CardProps as DSCardProps,
  StatTileProps as DSStatTileProps,
  ButtonProps as DSButtonProps,
  InputProps as DSInputProps,
  GlassCardProps as DSGlassCardProps,
  AvatarProps as DSAvatarProps,
} from '../design-system/Templates';

// Design System Utilities
export { default as ds } from '../../utils/designSystem';
export type { Tone } from '../../utils/designSystem';

// Story Components
export {
  StoryHeader,
  StoryTimelineItem,
  StorySectionEditor,
  StoryProgressBar,
  StoryNavigation,
  StoryFooter,
  StoryIntroCard,
  StoryFooterCard,
} from './StoryComponents';

// Authentication Components
export {
  AuthScreenLayout,
  AuthHeader,
  AuthInput,
  AuthButton,
  FeatureCard,
  FeatureShowcase,
  OnboardingStep,
  OnboardingNavigation,
} from './AuthComponents';

// Legacy Common Components (for backward compatibility)
export {
  Card,
  GradientButton as LegacyGradientButton,
  StyledInput,
  Avatar,
  SectionHeader,
  ProgressBar as LegacyProgressBar,
  IconText,
  CommonModal,
  EmptyState,
  StatCard as LegacyStatCard,
  SearchBar,
  QuickActionButton,
} from './CommonComponents';

// Design System Top Tabs (shared)
export { default as TopTabs } from './TopTabs';
export type { TopTabsProps, TopTabItem } from './TopTabs';
// Surprise Me Component
export { default as SurpriseMe } from './SurpriseMe';
export type { SurpriseMeProps, SurpriseItem } from './SurpriseMe';

// Heart Toggle Component
export { default as HeartToggle } from './HeartToggle';
export type { HeartToggleProps } from './HeartToggle';

// Unified Favorites System
export {
  useFavorites,
  useMealFavorites,
  useDateNightFavorites,
  useMemoryFavorites,
  useCoupleProfileSaves,
  usePlaylistSongFavorites,
} from '../../hooks/shared/useContentFavorites';

export type {
  UseFavoritesOptions,
  UseFavoritesReturn
} from '../../hooks/shared/useFavorites';

export type {
  FavoriteContentType,
  FavoriteItem as FavoriteServiceItem,
  CreateFavoriteData,
} from '../../services/favoritesService';

// Week Screen Layout Component
export { WeekScreenLayout } from './WeekScreenLayout';
export type { WeekScreenLayoutProps, WeekStep } from './WeekScreenLayout';

// Couple Profile Components (new)
export {
  ProfileHeader,
  StatsOverview,
  FavoritesSection,
  AchievementsSection,
} from './CoupleComponents';
export type {
  ProfileHeaderProps,
  StatsOverviewProps,
  FavoriteItem,
  FavoritesSectionProps,
  AchievementItem,
  AchievementsSectionProps,
} from './CoupleComponents';

// Form Components
export {
  FormInput,
  FormButton,
  FormSection,
  FormCard,
} from './FormComponents';
export type {
  FormInputProps,
  FormButtonProps,
  FormSectionProps,
  FormCardProps,
} from './FormComponents';

// Re-export types for convenience
export type {
  // Enhanced Components Types
  GradientCardProps,
  StatCardProps,
  GradientButtonProps,
  ProgressBarProps,
  WeekSelectorProps,
  EnhancedModalProps,
  EnhancedInputProps,
  ActivityCardProps,
  StepNavigationProps,
} from './EnhancedComponents';

export type {
  // Layout Components Types
  ScreenLayoutProps,
  HeaderLayoutProps,
  ContentLayoutProps,
  CardGridProps,
  SectionLayoutProps,
  FlexRowProps,
  FlexColumnProps,
  SpacerProps,
  DividerProps,
} from './LayoutComponents';

export type {
  // Story Components Types
  StorySection,
  StoryHeaderProps,
  StoryTimelineItemProps,
  StorySectionEditorProps,
  StoryProgressBarProps,
  StoryNavigationProps,
} from './StoryComponents';
