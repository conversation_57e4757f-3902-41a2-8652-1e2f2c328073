import React, { useState, useCallback } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  AccessibilityInfo,
} from 'react-native';
import { Heart } from 'lucide-react-native';
import { colors } from '../../utils/colors';

export interface HeartToggleProps {
  /** Whether the item is currently favorited */
  isFavorited: boolean;
  /** Callback when the heart is toggled */
  onToggle: (isFavorited: boolean) => Promise<void>;
  /** Optional callback for when toggle completes successfully */
  onChange?: (isFavorited: boolean) => void;
  /** Size of the heart icon */
  size?: number;
  /** Custom styles for the container */
  style?: ViewStyle;
  /** Accessibility label for the heart button */
  accessibilityLabel?: string;
  /** Whether the toggle is disabled */
  disabled?: boolean;
}

export const HeartToggle: React.FC<HeartToggleProps> = ({
  isFavorited,
  onToggle,
  onChange,
  size = 24,
  style,
  accessibilityLabel,
  disabled = false,
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handlePress = useCallback(async () => {
    if (disabled || isLoading) return;

    setIsLoading(true);
    setIsPressed(true);

    try {
      const newFavoritedState = !isFavorited;
      await onToggle(newFavoritedState);
      onChange?.(newFavoritedState);
    } catch (error) {
      console.error('Error toggling heart:', error);
      // Error handling is done in the hook, so we don't need to do anything here
    } finally {
      setIsLoading(false);
      // Reset pressed state after a short delay for visual feedback
      setTimeout(() => setIsPressed(false), 150);
    }
  }, [isFavorited, onToggle, onChange, disabled, isLoading]);

  const heartColor = isFavorited ? colors.error : colors.white;
  const heartFill = isFavorited ? colors.error : 'none';

  return (
    <TouchableOpacity
      style={[
        styles.heartToggle,
        isPressed && styles.heartPressed,
        disabled && styles.heartDisabled,
        style,
      ]}
      onPress={handlePress}
      disabled={disabled || isLoading}
      activeOpacity={0.7}
      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      accessibilityRole="button"
      accessibilityState={{
        disabled: disabled || isLoading,
      }}
      accessibilityLabel={
        accessibilityLabel ||
        `${isFavorited ? 'Remove from' : 'Add to'} favorites`
      }
      accessibilityHint={`${isFavorited ? 'Remove' : 'Add'} this item to your favorites`}
    >
      <Heart
        size={size}
        color={heartColor}
        fill={heartFill}
        strokeWidth={2}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  heartToggle: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 1,
    padding: 4,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 32,
    minHeight: 32,
  },
  heartPressed: {
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    transform: [{ scale: 0.95 }],
  },
  heartDisabled: {
    opacity: 0.5,
  },
});

export default HeartToggle;
