/**
 * Story Components
 * 
 * Shared components for story functionality across the app.
 * Consolidates common patterns from our-story.tsx and Story.tsx.
 * 
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  TextInput, 
  StyleSheet,
  ScrollView 
} from 'react-native';

import { 
  Heart, 
  ArrowLeft, 
  BookOpen, 
  Edit3, 
  Calendar, 
  ArrowRight, 
  SkipForward,
  Eye,
  Save
} from 'lucide-react-native';
import { colors } from '../../utils/colors';
import PhotoManager from '../PhotoManager';

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface StorySection {
  id: string;
  title: string;
  subtitle: string;
  content: string;
  photos: any[];
  icon: string;
  color: string;
  date?: string;
  field: string;
  photoField: string;
  placeholder?: string;
}

export interface StoryHeaderProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  onBack: () => void;
  onEdit?: () => void;
  onView?: () => void;
  isEditMode?: boolean;
  showProgress?: boolean;
  progressPercentage?: number;
  backgroundColor?: string;
}

export interface StoryTimelineItemProps {
  section: StorySection;
  index: number;
  totalSections: number;
  mode: 'view' | 'edit';
  onFieldUpdate?: (field: any, value: string) => void;
  onAddPhoto?: (field: any, photo: any) => void;
  onRemovePhoto?: (field: any, photoId: string) => void;
}

export interface StorySectionEditorProps {
  section: StorySection;
  onFieldUpdate: (field: any, value: string) => void;
  onAddPhoto: (field: any, photo: any) => void;
  onRemovePhoto: (field: any, photoId: string) => void;
  maxLength?: number;
}

export interface StoryProgressBarProps {
  current: number;
  total: number;
  showPercentage?: boolean;
  backgroundColor?: string;
  fillColor?: string;
}

export interface StoryNavigationProps {
  currentSection: number;
  totalSections: number;
  onPrevious: () => void;
  onNext: () => void;
  onSave: () => void;
  onSkip: () => void;
  isLastSection?: boolean;
  disabled?: boolean;
}

// ============================================================================
// STORY HEADER COMPONENT
// ============================================================================

export const StoryHeader: React.FC<StoryHeaderProps> = ({
  title,
  subtitle,
  icon,
  onBack,
  onEdit,
  onView,
  isEditMode = false,
  showProgress = false,
  progressPercentage = 0,
  backgroundColor = colors.primary
}) => {
  return (
    <View
      style={[styles.header, { backgroundColor }]}
    >
      <View style={styles.headerContent}>
        <TouchableOpacity style={styles.headerButton} onPress={onBack}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        
        <View style={styles.titleContainer}>
          {icon || <BookOpen size={32} color={colors.white} />}
          <Text style={styles.headerTitle}>{title}</Text>
          {subtitle && <Text style={styles.headerSubtitle}>{subtitle}</Text>}
        </View>
        
        {isEditMode ? (
          showProgress ? (
            <View style={styles.progressContainer}>
              <Text style={styles.progressText}>
                {progressPercentage}% Complete
              </Text>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill, 
                    { width: `${progressPercentage}%` }
                  ]} 
                />
              </View>
            </View>
          ) : (
            <TouchableOpacity style={styles.headerButton} onPress={onView}>
              <Eye size={24} color={colors.white} />
            </TouchableOpacity>
          )
        ) : (
          <TouchableOpacity style={styles.headerButton} onPress={onEdit}>
            <Edit3 size={24} color={colors.white} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

// ============================================================================
// STORY TIMELINE ITEM COMPONENT
// ============================================================================

export const StoryTimelineItem: React.FC<StoryTimelineItemProps> = ({
  section,
  index,
  totalSections,
  mode,
  onFieldUpdate,
  onAddPhoto,
  onRemovePhoto
}) => {
  return (
    <View style={styles.timelineItem}>
      <View style={styles.timelineLeft}>
        <View style={[styles.timelineIcon, { backgroundColor: section.color }]}>
          <Text style={styles.timelineIconText}>{section.icon}</Text>
        </View>
        {index < totalSections - 1 && (
          <View style={styles.timelineLine} />
        )}
      </View>
      
      <View style={styles.timelineContent}>
        <View style={styles.timelineCard}>
          <View style={styles.timelineHeader}>
            <Text style={styles.timelineTitle}>{section.title}</Text>
            <Text style={styles.timelineSubtitle}>{section.subtitle}</Text>
          </View>
          
          {mode === 'edit' ? (
            <TextInput
              style={[
                styles.textInput,
                section.content ? styles.textInputFilled : styles.textInputEmpty,
                { color: colors.textPrimary }
              ]}
              placeholder={section.placeholder || `Share your story about ${section.title.toLowerCase()}...`}
              placeholderTextColor={colors.textSecondary}
              value={section.content}
              onChangeText={(text) => onFieldUpdate?.(section.field, text)}
              multiline
              numberOfLines={4}
              maxLength={2000}
            />
          ) : (
            <Text style={styles.timelineText}>
              {section.content || `Share your story about ${section.title.toLowerCase()}...`}
            </Text>
          )}
          
          <PhotoManager 
            photos={section.photos} 
            sectionTitle={section.title} 
            mode={mode}
            onAddPhoto={(photo) => {
              const convertedPhoto = {
                id: photo.id,
                uri: photo.url || photo.localUri || photo.uri || '',
                timestamp: photo.timestamp,
                caption: photo.caption
              };
              onAddPhoto?.(section.photoField, convertedPhoto);
            }}
            onRemovePhoto={(photoId) => onRemovePhoto?.(section.photoField, photoId)}
          />
          
          {mode === 'view' && section.date && (
            <View style={styles.timelineFooter}>
              <View style={styles.timelineDate}>
                <Calendar size={14} color={colors.textTertiary} />
                <Text style={styles.timelineDateText}>{section.date}</Text>
              </View>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

// ============================================================================
// STORY SECTION EDITOR COMPONENT
// ============================================================================

export const StorySectionEditor: React.FC<StorySectionEditorProps> = ({
  section,
  onFieldUpdate,
  onAddPhoto,
  onRemovePhoto,
  maxLength = 2000
}) => {
  return (
    <View style={styles.sectionEditor}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{section.title}</Text>
        <Text style={styles.sectionSubtitle}>{section.subtitle}</Text>
      </View>
      
      <TextInput
        style={[
          styles.textInput,
          section.content ? styles.textInputFilled : styles.textInputEmpty,
          { color: colors.textPrimary }
        ]}
        placeholder={section.placeholder || `Share your story about ${section.title.toLowerCase()}...`}
        placeholderTextColor={colors.textSecondary}
        value={section.content}
        onChangeText={(text) => onFieldUpdate(section.field, text)}
        multiline
        numberOfLines={6}
        maxLength={maxLength}
        textAlignVertical="top"
      />
      
      <Text style={styles.characterCount}>
        {section.content.length}/{maxLength} characters
      </Text>
      
      <PhotoManager
        photos={section.photos}
        sectionTitle={section.title}
        mode="edit"
        onAddPhoto={(photo) => {
          const convertedPhoto = {
            id: photo.id,
            uri: photo.url || photo.localUri || photo.uri || '',
            timestamp: photo.timestamp,
            caption: photo.caption
          };
          onAddPhoto(section.photoField, convertedPhoto);
        }}
        onRemovePhoto={(photoId) => onRemovePhoto(section.photoField, photoId)}
        maxPhotos={5}
      />
    </View>
  );
};

// ============================================================================
// STORY PROGRESS BAR COMPONENT
// ============================================================================

export const StoryProgressBar: React.FC<StoryProgressBarProps> = ({
  current,
  total,
  showPercentage = false,
  backgroundColor = 'rgba(255, 255, 255, 0.3)',
  fillColor = colors.white
}) => {
  const progress = total > 0 ? (current / total) * 100 : 0;
  
  return (
    <View style={styles.progressContainer}>
      <View style={[styles.progressBar, { backgroundColor }]}>
        <View 
          style={[
            styles.progressFill, 
            { 
              width: `${progress}%`,
              backgroundColor: fillColor
            }
          ]} 
        />
      </View>
      {showPercentage && (
        <Text style={styles.progressText}>{Math.round(progress)}%</Text>
      )}
    </View>
  );
};

// ============================================================================
// STORY NAVIGATION COMPONENT
// ============================================================================

export const StoryNavigation: React.FC<StoryNavigationProps> = ({
  currentSection,
  totalSections,
  onPrevious,
  onNext,
  onSave,
  onSkip,
  isLastSection = false,
  disabled = false
}) => {
  return (
    <View style={styles.navigation}>
      <TouchableOpacity
        style={[styles.navButton, currentSection === 0 && styles.navButtonDisabled]}
        onPress={onPrevious}
        disabled={currentSection === 0 || disabled}
      >
        <ArrowLeft size={20} color={currentSection === 0 ? colors.textTertiary : colors.white} />
        <Text style={[styles.navButtonText, currentSection === 0 && styles.navButtonTextDisabled]}>
          Previous
        </Text>
      </TouchableOpacity>

      {isLastSection ? (
        <TouchableOpacity 
          style={[styles.saveButton, disabled && styles.navButtonDisabled]} 
          onPress={onSave}
          disabled={disabled}
        >
          <Save size={20} color={colors.white} />
          <Text style={styles.saveButtonText}>Save Story</Text>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity 
          style={[styles.navButton, disabled && styles.navButtonDisabled]} 
          onPress={onNext}
          disabled={disabled}
        >
          <Text style={styles.navButtonText}>Next</Text>
          <ArrowRight size={20} color={colors.white} />
        </TouchableOpacity>
      )}
    </View>
  );
};

// ============================================================================
// STORY FOOTER COMPONENT
// ============================================================================

export const StoryFooter: React.FC<{ onSkip: () => void; onSave: () => void }> = ({
  onSkip,
  onSave
}) => {
  return (
    <View style={styles.footerContainer}>
      <TouchableOpacity style={styles.skipButton} onPress={onSkip}>
        <SkipForward size={20} color={colors.textSecondary} />
        <Text style={styles.skipButtonText}>Skip for Now</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.saveButton} onPress={onSave}>
        <View
          style={[styles.saveButtonGradient, { backgroundColor: colors.primary }]}
        >
          <Text style={styles.saveButtonText}>Save & Continue</Text>
          <ArrowRight size={20} color={colors.white} />
        </View>
      </TouchableOpacity>
    </View>
  );
};

// ============================================================================
// STORY INTRO CARD COMPONENT
// ============================================================================

export const StoryIntroCard: React.FC<{
  title?: string;
  text: string;
  mode?: 'view' | 'edit';
}> = ({ title, text, mode = 'view' }) => {
  return (
    <View style={styles.introCard}>
      {title && <Text style={styles.introTitle}>{title}</Text>}
      <Text style={styles.introText}>{text}</Text>
    </View>
  );
};

// ============================================================================
// STORY FOOTER CARD COMPONENT
// ============================================================================

export const StoryFooterCard: React.FC = () => {
  return (
    <View style={styles.footerCard}>
      <View style={styles.footerIcon}>
        <Heart size={32} color={colors.lightPink} />
      </View>
      <Text style={styles.footerTitle}>Our Story Continues...</Text>
      <Text style={styles.footerText}>
        Every day we write new chapters in our love story. Keep growing, keep loving, and keep creating beautiful memories together.
      </Text>
    </View>
  );
};

// ============================================================================
// STYLES
// ============================================================================

const styles = StyleSheet.create({
  // Header styles
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleContainer: {
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    textAlign: 'center',
    marginTop: 8,
  },
  headerSubtitle: {
    fontSize: 14,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
    marginTop: 4,
  },
  progressContainer: {
    alignItems: 'center',
  },
  progressText: {
    fontSize: 14,
    color: colors.white,
    opacity: 0.9,
    marginBottom: 8,
  },
  progressBar: {
    width: 120,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 2,
  },

  // Timeline styles
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: 20,
  },
  timelineIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  timelineIconText: {
    fontSize: 24,
  },
  timelineLine: {
    width: 2,
    height: 60,
    backgroundColor: '#E5E7EB',
    marginTop: 8,
  },
  timelineContent: {
    flex: 1,
  },
  timelineCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  timelineHeader: {
    marginBottom: 12,
  },
  timelineTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  timelineSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
  },
  timelineText: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    marginBottom: 16,
  },
  timelineFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  timelineDate: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timelineDateText: {
    fontSize: 12,
    color: '#9CA3AF',
    marginLeft: 6,
    fontStyle: 'italic',
  },

  // Section editor styles
  sectionEditor: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionHeader: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
  },

  // Input styles
  textInput: {
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#F9FAFB',
    color: '#374151',
    textAlignVertical: 'top',
    minHeight: 100,
  },
  textInputEmpty: {
    borderColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
  },
  textInputFilled: {
    borderColor: colors.primary,
    backgroundColor: colors.backgroundSecondary,
  },
  characterCount: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'right',
    marginTop: 8,
  },

  // Navigation styles
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: colors.backgroundPrimary,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  navButtonDisabled: {
    backgroundColor: colors.backgroundTertiary,
  },
  navButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  navButtonTextDisabled: {
    color: colors.textTertiary,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.success,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },

  // Footer styles
  footerContainer: {
    marginBottom: 40,
  },
  skipButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
  },
  skipButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
    marginLeft: 8,
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },

  // Card styles
  introCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    alignItems: 'center',
  },
  introTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 12,
    textAlign: 'center',
  },
  introText: {
    fontSize: 16,
    color: '#6B7280',
    lineHeight: 24,
    textAlign: 'center',
  },
  footerCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    marginBottom: 40,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    alignItems: 'center',
  },
  footerIcon: {
    marginBottom: 16,
  },
  footerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 12,
    textAlign: 'center',
  },
  footerText: {
    fontSize: 16,
    color: '#6B7280',
    lineHeight: 24,
    textAlign: 'center',
  },
});
