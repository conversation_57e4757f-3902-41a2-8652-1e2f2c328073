import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';

import { auth, type SignUpData, type SignInData } from '../services/auth';
import { logger } from '../utils/logger';
import { simpleErrorService } from '../services/simpleErrorService';
import { colors } from '../utils/colors';
import { logEvent } from '../lib/analytics/eventLogger';

interface AuthScreenProps {
  onAuthSuccess: () => void;
}

export default function AuthScreen({ onAuthSuccess }: AuthScreenProps) {
  const [activeTab, setActiveTab] = useState<'signin' | 'signup'>('signin');
  const [isLoading, setIsLoading] = useState(false);
  // Sign up form state
  const [signUpData, setSignUpData] = useState<SignUpData>({
    email: '',
    password: '',
    partner1Name: '',
    partner1Icon: '👤',
    partner2Name: '',
    partner2Icon: '👤',
  });
  
  // Sign in form state
  const [signInData, setSignInData] = useState<SignInData>({
    email: '',
    password: '',
  });

  const handleSignUp = async () => {
    if (!signUpData.email || !signUpData.password || !signUpData.partner1Name || !signUpData.partner2Name) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (signUpData.password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters long');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(signUpData.email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    try {
      console.log('🔍 Attempting signup with:', { 
        email: signUpData.email, 
        hasPassword: !!signUpData.password,
        partner1Name: signUpData.partner1Name,
        partner2Name: signUpData.partner2Name
      });
      
      const authData = await auth.signUp(signUpData);
      
      console.log('✅ Signup successful:', authData.user?.id);
      
      // Log account creation event
      await logEvent('account_created');


      
      // Check if email confirmation is required
      if (authData.user && !authData.user.email_confirmed_at) {
        Alert.alert(
          'Check Your Email', 
          'We sent you a confirmation link. Please check your email and click the link to verify your account.',
          [{ text: 'OK', onPress: () => onAuthSuccess() }]
        );
      } else {
        Alert.alert('Success', 'Account created successfully!');
        onAuthSuccess();
      }
    } catch (error: any) {
      console.error('❌ Sign up error:', error);
      logger.error('Sign up error:', error);
      
      // Report the error for debugging
      await simpleErrorService.reportError(error, {
        component: 'AuthScreen',
        action: 'handleSignUp',
        metadata: { 
          email: signUpData.email,
          hasPassword: !!signUpData.password,
          partner1Name: signUpData.partner1Name,
          partner2Name: signUpData.partner2Name
        },
      });
      
      // Handle specific error cases
      let errorMessage = 'Failed to create account';
      if (error.message) {
        if (error.message.includes('email_address_invalid')) {
          errorMessage = 'Please enter a valid email address (try using gmail.com, yahoo.com, etc.)';
        } else if (error.message.includes('already_registered')) {
          errorMessage = 'An account with this email already exists';
        } else if (error.message.includes('password')) {
          errorMessage = 'Password must be at least 6 characters long';
        } else if (error.message.includes('Invalid email')) {
          errorMessage = 'Please enter a valid email address';
        } else {
          errorMessage = error.message;
        }
      }
      
      Alert.alert('Error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignIn = async () => {
    if (!signInData.email || !signInData.password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(signInData.email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    try {
      console.log('🔍 Attempting signin with:', { 
        email: signInData.email, 
        hasPassword: !!signInData.password
      });
      
      const authData = await auth.signIn(signInData);
      
      console.log('✅ Signin successful:', authData.user?.id);
      
      // Log successful sign in event (queued if needed and flushed by listener)
      await logEvent('sign_in_success');

      onAuthSuccess();
    } catch (error: any) {
      console.error('❌ Sign in error:', error);
      logger.error('Sign in error:', error);
      
      // Report the error for debugging
      await simpleErrorService.reportError(error, {
        component: 'AuthScreen',
        action: 'handleSignIn',
        metadata: { 
          email: signInData.email,
          hasPassword: !!signInData.password
        },
      });
      
      // Handle specific error cases
      let errorMessage = 'Failed to sign in';
      if (error.message) {
        if (error.message.includes('Invalid login credentials')) {
          errorMessage = 'Invalid email or password';
        } else if (error.message.includes('Email not confirmed')) {
          errorMessage = 'Please check your email and confirm your account';
        } else if (error.message.includes('Too many requests')) {
          errorMessage = 'Too many attempts. Please try again later';
        } else {
          errorMessage = error.message;
        }
      }
      
      Alert.alert('Error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!signInData.email) {
      Alert.alert('Error', 'Please enter your email address first');
      return;
    }

    try {
      await auth.resetPassword(signInData.email);
      Alert.alert('Success', 'Password reset email sent! Check your inbox.');
    } catch (error: any) {
      logger.error('Reset password error:', error);
      Alert.alert('Error', error.message || 'Failed to send reset email');
    }
  };

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'signin' && styles.activeTab]}
        onPress={() => setActiveTab('signin')}
      >
        <Text style={[styles.tabText, activeTab === 'signin' && styles.activeTabText]}>
          Sign In
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'signup' && styles.activeTab]}
        onPress={() => setActiveTab('signup')}
      >
        <Text style={[styles.tabText, activeTab === 'signup' && styles.activeTabText]}>
          Sign Up
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderSignUpForm = () => (
    <View style={styles.formContainer}>
      <Text style={styles.title}>Create Account</Text>
      <Text style={styles.subtitle}>Start your journey together</Text>
      
      <TextInput
        style={styles.input}
        placeholder="Email"
        value={signUpData.email}
        onChangeText={(text) => setSignUpData({ ...signUpData, email: text })}
        keyboardType="email-address"
        autoCapitalize="none"
        autoCorrect={false}
      />
      
      <TextInput
        style={styles.input}
        placeholder="Password (min 6 characters)"
        value={signUpData.password}
        onChangeText={(text) => setSignUpData({ ...signUpData, password: text })}
        secureTextEntry
        autoCapitalize="none"
        autoCorrect={false}
      />
      
      <TextInput
        style={styles.input}
        placeholder="Partner 1 Name"
        value={signUpData.partner1Name}
        onChangeText={(text) => setSignUpData({ ...signUpData, partner1Name: text })}
        autoCapitalize="words"
      />
      
      <TextInput
        style={styles.input}
        placeholder="Partner 2 Name"
        value={signUpData.partner2Name}
        onChangeText={(text) => setSignUpData({ ...signUpData, partner2Name: text })}
        autoCapitalize="words"
      />
      
      <TouchableOpacity
        style={[styles.button, isLoading && styles.buttonDisabled]}
        onPress={handleSignUp}
        disabled={isLoading}
      >
        <Text style={styles.buttonText}>
          {isLoading ? 'Creating Account...' : 'Create Account'}
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.linkButton}
        onPress={() => setActiveTab('signin')}
      >
        <Text style={styles.linkText}>Already have an account? Sign In</Text>
      </TouchableOpacity>
    </View>
  );

  const renderSignInForm = () => (
    <View style={styles.formContainer}>
      <Text style={styles.title}>Welcome Back</Text>
      <Text style={styles.subtitle}>Continue your journey together</Text>
      
      <TextInput
        style={styles.input}
        placeholder="Email"
        value={signInData.email}
        onChangeText={(text) => setSignInData({ ...signInData, email: text })}
        keyboardType="email-address"
        autoCapitalize="none"
        autoCorrect={false}
      />
      
      <TextInput
        style={styles.input}
        placeholder="Password"
        value={signInData.password}
        onChangeText={(text) => setSignInData({ ...signInData, password: text })}
        secureTextEntry
        autoCapitalize="none"
        autoCorrect={false}
      />
      
      <TouchableOpacity
        style={[styles.button, isLoading && styles.buttonDisabled]}
        onPress={handleSignIn}
        disabled={isLoading}
      >
        <Text style={styles.buttonText}>
          {isLoading ? 'Signing In...' : 'Sign In'}
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.linkButton}
        onPress={handleForgotPassword}
      >
        <Text style={styles.linkText}>Forgot Password?</Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.linkButton}
        onPress={() => setActiveTab('signup')}
      >
        <Text style={styles.linkText}>Don't have an account? Sign Up</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View
      style={[styles.container, { backgroundColor: colors.primary }]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.logoContainer}>
            <Text style={styles.logo}>💕</Text>
            <Text style={styles.appName}>Everlasting Us</Text>
          </View>
          
          {renderTabBar()}
          {activeTab === 'signup' ? renderSignUpForm() : renderSignInForm()}
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    fontSize: 60,
    marginBottom: 10,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
  formContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 30,
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.textPrimary,
    textAlign: 'center',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 30,
  },
  input: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    fontSize: 16,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  button: {
    backgroundColor: colors.indigo,
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginBottom: 15,
  },
  buttonDisabled: {
    backgroundColor: colors.borderMedium,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  linkButton: {
    alignItems: 'center',
    marginBottom: 10,
  },
  linkText: {
    color: colors.indigo,
    fontSize: 14,
    textDecorationLine: 'underline',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    padding: 4,
    marginBottom: 20,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: 'white',
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  activeTabText: {
    color: colors.indigo,
    fontWeight: '600',
  },
});
