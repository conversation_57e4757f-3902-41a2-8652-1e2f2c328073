# Everlasting Us - Simple Build Guide

## 🚀 Essential Commands

### Daily Development
```bash
# Start development (most common command)
npm run dev

# If you have issues, clear cache and restart
npx expo start --clear
```

### Testing on Devices
```bash
# Mobile: Scan QR code with Expo Go app
npm run dev

# Web browser
npx expo start --web

# iOS Simulator (Mac only)
npx expo start --ios

# Android Emulator
npx expo start --android
```

### Build for Production
```bash
# Web deployment
npm run build:web

# Mobile apps (when ready for app stores)
npx expo build:ios
npx expo build:android
```

## 🛠️ When Things Go Wrong

### Cache Issues (try in this order)
```bash
# 1. Clear Metro cache (fixes 80% of issues)
npx expo start --clear

# 2. If still broken, nuclear option
rm -rf node_modules
npm install
npx expo start --clear
```

### Common Problems
- **App not updating?** → Use `npx expo start --clear`
- **Build failing?** → Try `npm run build:web` after clearing cache
- **Weird errors?** → Delete `node_modules` folder and run `npm install`

## 📱 Quick Setup
```bash
# First time setup
npm install
npm run dev
```

## 🎯 That's It!

**90% of the time you'll use:** `npm run dev`

**When it breaks:** `npx expo start --clear`

**For production:** `npm run build:web`