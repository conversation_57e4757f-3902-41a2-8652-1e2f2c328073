import { useState, useEffect, useCallback } from 'react';
import { mealIdeasService, MealIdea, GlobalMealIdea, UserMealIdea } from '../services/mealIdeasService';
import { useAuth } from './useAuth';
import { logger } from '../utils/logger';

export interface MealIdeasState {
  globalIdeas: GlobalMealIdea[];
  userIdeas: UserMealIdea[];
  combinedIdeas: MealIdea[];
  categories: string[];
  isLoading: boolean;
  error: string | null;
}

export const useMealIdeasSupabase = () => {
  const [state, setState] = useState<MealIdeasState>({
    globalIdeas: [],
    userIdeas: [],
    combinedIdeas: [],
    categories: [],
    isLoading: true,
    error: null,
  });

  const { user } = useAuth();

  // Load meal ideas data on mount
  useEffect(() => {
    if (user) {
      loadMealIdeas();
    } else {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [user]);

  const loadMealIdeas = async (category?: string) => {
    if (!user) return;

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const [globalIdeas, userIdeas, combinedIdeas, categories] = await Promise.all([
        mealIdeasService.getGlobalMealIdeas(category),
        mealIdeasService.getUserMealIdeas(user.id, category),
        mealIdeasService.getCombinedMealIdeas(user.id, category),
        mealIdeasService.getMealCategories()
      ]);

      setState({
        globalIdeas,
        userIdeas,
        combinedIdeas,
        categories,
        isLoading: false,
        error: null,
      });

      logger.info('Meal ideas loaded successfully');
    } catch (error) {
      logger.error('Error loading meal ideas:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to load meal ideas',
      }));
    }
  };

  const createUserMealIdea = useCallback(async (mealIdea: Omit<UserMealIdea, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<boolean> => {
    if (!user) {
      logger.warn('No user logged in, cannot create meal idea');
      return false;
    }

    try {
      const result = await mealIdeasService.createUserMealIdea(user.id, mealIdea);
      
      if (result) {
        // Reload data to get updated ideas
        await loadMealIdeas();
      }
      
      return !!result;
    } catch (error) {
      logger.error('Error creating meal idea:', error);
      return false;
    }
  }, [user]);

  const updateUserMealIdea = useCallback(async (ideaId: string, updates: Partial<UserMealIdea>): Promise<boolean> => {
    if (!user) {
      logger.warn('No user logged in, cannot update meal idea');
      return false;
    }

    try {
      const success = await mealIdeasService.updateUserMealIdea(user.id, ideaId, updates);
      
      if (success) {
        // Reload data to get updated ideas
        await loadMealIdeas();
      }
      
      return success;
    } catch (error) {
      logger.error('Error updating meal idea:', error);
      return false;
    }
  }, [user]);

  const deleteUserMealIdea = useCallback(async (ideaId: string): Promise<boolean> => {
    if (!user) {
      logger.warn('No user logged in, cannot delete meal idea');
      return false;
    }

    try {
      const success = await mealIdeasService.deleteUserMealIdea(user.id, ideaId);
      
      if (success) {
        // Reload data to get updated ideas
        await loadMealIdeas();
      }
      
      return success;
    } catch (error) {
      logger.error('Error deleting meal idea:', error);
      return false;
    }
  }, [user]);


  const markAsCompleted = useCallback(async (ideaId: string): Promise<boolean> => {
    if (!user) {
      logger.warn('No user logged in, cannot mark as completed');
      return false;
    }

    try {
      const success = await mealIdeasService.markAsCompleted(user.id, ideaId);
      
      if (success) {
        // Reload data to get updated ideas
        await loadMealIdeas();
      }
      
      return success;
    } catch (error) {
      logger.error('Error marking as completed:', error);
      return false;
    }
  }, [user]);

  const searchMealIdeas = useCallback(async (searchTerm: string, category?: string): Promise<MealIdea[]> => {
    if (!user) {
      logger.warn('No user logged in, cannot search meal ideas');
      return [];
    }

    try {
      return await mealIdeasService.searchMealIdeas(user.id, searchTerm, category);
    } catch (error) {
      logger.error('Error searching meal ideas:', error);
      return [];
    }
  }, [user]);

  const getIdeasByCategory = useCallback((category: string): MealIdea[] => {
    return state.combinedIdeas.filter(idea => idea.category === category);
  }, [state.combinedIdeas]);

  const getFavoriteIdeas = useCallback((): MealIdea[] => {
    return state.combinedIdeas.filter(idea => idea.isFavorite);
  }, [state.combinedIdeas]);

  const getCompletedIdeas = useCallback((): MealIdea[] => {
    return state.combinedIdeas.filter(idea => idea.isCompleted);
  }, [state.combinedIdeas]);

  const refreshData = useCallback(() => {
    loadMealIdeas();
  }, []);

  return {
    // State
    ...state,
    
    // Actions
    createUserMealIdea,
    updateUserMealIdea,
    deleteUserMealIdea,
    markAsCompleted,
    searchMealIdeas,
    refreshData,
    loadMealIdeas,
    
    // Helper functions
    getIdeasByCategory,
    getFavoriteIdeas,
    getCompletedIdeas,
  };
};
