import { useState, useEffect } from 'react';
import { dateNightIdeasService, DateNightIdea, UserDateNightIdea } from '../services/dateNightIdeasService';
import { useAuth } from './useAuth';
import { logger } from '../utils/logger';

export interface DateNightIdeasState {
  allIdeas: DateNightIdea[];
  weeklyIdeas: DateNightIdea[];
  userIdeas: UserDateNightIdea[];
  categories: string[];
  isLoading: boolean;
  error: string | null;
}

export const useDateNightIdeasSupabase = () => {
  const [state, setState] = useState<DateNightIdeasState>({
    allIdeas: [],
    weeklyIdeas: [],
    userIdeas: [],
    categories: [],
    isLoading: true,
    error: null,
  });

  const { user } = useAuth();

  // Load all data on mount
  useEffect(() => {
    loadAllData();
  }, [user]);

  const loadAllData = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const [allIdeas, weeklyIdeas, categories, userIdeas] = await Promise.all([
        dateNightIdeasService.getAllIdeas(),
        dateNightIdeasService.getWeeklyIdeas(),
        dateNightIdeasService.getCategories(),
        user ? dateNightIdeasService.getUserIdeas(user.id) : Promise.resolve([]),
      ]);

      setState({
        allIdeas,
        weeklyIdeas,
        userIdeas,
        categories,
        isLoading: false,
        error: null,
      });

      logger.info('Date night ideas loaded successfully');
    } catch (error) {
      logger.error('Error loading date night ideas:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to load date night ideas',
      }));
    }
  };

  const getRandomIdeas = async (count: number = 5): Promise<DateNightIdea[]> => {
    try {
      return await dateNightIdeasService.getRandomIdeas(count);
    } catch (error) {
      logger.error('Error getting random ideas:', error);
      return [];
    }
  };

  const getIdeasByCategory = async (category: string): Promise<DateNightIdea[]> => {
    try {
      return await dateNightIdeasService.getIdeasByCategory(category);
    } catch (error) {
      logger.error('Error getting ideas by category:', error);
      return [];
    }
  };

  const getIdeasByDifficulty = async (difficulty: 'easy' | 'medium' | 'hard'): Promise<DateNightIdea[]> => {
    try {
      return await dateNightIdeasService.getIdeasByDifficulty(difficulty);
    } catch (error) {
      logger.error('Error getting ideas by difficulty:', error);
      return [];
    }
  };

  const searchIdeas = async (query: string): Promise<DateNightIdea[]> => {
    try {
      return await dateNightIdeasService.searchIdeas(query);
    } catch (error) {
      logger.error('Error searching ideas:', error);
      return [];
    }
  };

  const saveUserIdea = async (ideaId: string, status: 'planned' | 'completed' | 'favorite', notes?: string, rating?: number): Promise<boolean> => {
    if (!user) {
      logger.warn('No user logged in, cannot save idea');
      return false;
    }

    try {
      const success = await dateNightIdeasService.saveUserIdea(user.id, ideaId, status, notes, rating);
      
      if (success) {
        // Reload user ideas to update the state
        const updatedUserIdeas = await dateNightIdeasService.getUserIdeas(user.id);
        setState(prev => ({ ...prev, userIdeas: updatedUserIdeas }));
      }
      
      return success;
    } catch (error) {
      logger.error('Error saving user idea:', error);
      return false;
    }
  };

  const removeUserIdea = async (ideaId: string): Promise<boolean> => {
    if (!user) {
      logger.warn('No user logged in, cannot remove idea');
      return false;
    }

    try {
      const success = await dateNightIdeasService.removeUserIdea(user.id, ideaId);
      
      if (success) {
        // Reload user ideas to update the state
        const updatedUserIdeas = await dateNightIdeasService.getUserIdeas(user.id);
        setState(prev => ({ ...prev, userIdeas: updatedUserIdeas }));
      }
      
      return success;
    } catch (error) {
      logger.error('Error removing user idea:', error);
      return false;
    }
  };

  const getUserIdeasByStatus = (status: 'planned' | 'completed' | 'favorite'): UserDateNightIdea[] => {
    return state.userIdeas.filter(idea => idea.status === status);
  };

  const getCompletedIdeas = (): UserDateNightIdea[] => {
    return getUserIdeasByStatus('completed');
  };

  const getFavoriteIdeas = (): UserDateNightIdea[] => {
    return getUserIdeasByStatus('favorite');
  };

  const getPlannedIdeas = (): UserDateNightIdea[] => {
    return getUserIdeasByStatus('planned');
  };

  const isIdeaSaved = (ideaId: string): boolean => {
    return state.userIdeas.some(idea => idea.ideaId === ideaId);
  };

  const getUserIdeaStatus = (ideaId: string): 'planned' | 'completed' | 'favorite' | null => {
    const userIdea = state.userIdeas.find(idea => idea.ideaId === ideaId);
    return userIdea ? userIdea.status : null;
  };

  const refreshData = () => {
    loadAllData();
  };

  return {
    // State
    ...state,
    
    // Actions
    getRandomIdeas,
    getIdeasByCategory,
    getIdeasByDifficulty,
    searchIdeas,
    saveUserIdea,
    removeUserIdea,
    refreshData,
    
    // Computed values
    getUserIdeasByStatus,
    getCompletedIdeas,
    getFavoriteIdeas,
    getPlannedIdeas,
    isIdeaSaved,
    getUserIdeaStatus,
  };
};
