import { useState, useEffect, useCallback } from 'react';
import { milestoneService, MilestoneTemplate, CoupleMilestone, MilestoneProgress } from '../services/milestoneService';
import { useAuth } from '../hooks/useAuth';
import { useCouplePairing } from './useCouplePairing';
import { logger } from '../utils/logger';

export interface UseRelationshipMilestonesOptions {
  /** Auto-load milestones on mount */
  autoLoad?: boolean;
  /** Filter by category */
  category?: string;
  /** Only load completed milestones */
  completedOnly?: boolean;
}

export interface UseRelationshipMilestonesReturn {
  // Data
  templates: MilestoneTemplate[];
  milestones: CoupleMilestone[];
  progress: MilestoneProgress[];
  
  // State
  isLoading: boolean;
  error: string | null;
  
  // Actions
  loadMilestones: () => Promise<void>;
  loadTemplates: () => Promise<void>;
  loadProgress: () => Promise<void>;
  updateMilestone: (milestoneId: string, data: Record<string, any>) => Promise<void>;
  getMilestoneByKey: (milestoneKey: string) => CoupleMilestone | undefined;
  getTemplateByKey: (milestoneKey: string) => MilestoneTemplate | undefined;
  initializeMilestones: () => Promise<void>;
  syncFromOriginStory: (originStoryData: any) => Promise<void>;
  refresh: () => Promise<void>;
  validateMilestoneData: (template: MilestoneTemplate, data: Record<string, any>) => ValidationResult;
  
  // Computed values
  totalMilestones: number;
  completedMilestones: number;
  overallProgress: number;
  milestonesByCategory: Record<string, CoupleMilestone[]>;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Hook for managing relationship milestones with templates and progress tracking
 */
export const useRelationshipMilestones = (options: UseRelationshipMilestonesOptions = {}): UseRelationshipMilestonesReturn => {
  const {
    autoLoad = true,
    category,
    completedOnly = false,
  } = options;

  const { user } = useAuth();
  const { couple } = useCouplePairing();

  // State
  const [templates, setTemplates] = useState<MilestoneTemplate[]>([]);
  const [milestones, setMilestones] = useState<CoupleMilestone[]>([]);
  const [progress, setProgress] = useState<MilestoneProgress[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load data on mount
  useEffect(() => {
    if (autoLoad && couple?.id) {
      loadAllData();
    }
  }, [couple?.id, category, completedOnly, autoLoad]);

  const loadAllData = useCallback(async () => {
    await Promise.all([
      loadTemplates(),
      loadMilestones(),
      loadProgress(),
    ]);
  }, []);

  const loadTemplates = useCallback(async () => {
    try {
      setError(null);
      
      const templatesData = category 
        ? await milestoneService.getMilestoneTemplatesByCategory(category)
        : await milestoneService.getMilestoneTemplates();
      
      setTemplates(templatesData);
      logger.info(`Loaded ${templatesData.length} milestone templates`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load templates';
      setError(errorMessage);
      logger.error('Error loading milestone templates:', err);
    }
  }, [category]);

  const loadMilestones = useCallback(async () => {
    if (!couple?.id) return;

    try {
      setIsLoading(true);
      setError(null);
      
      const milestonesData = completedOnly
        ? await milestoneService.getCompletedMilestones(couple.id)
        : await milestoneService.getCoupleMilestones(couple.id);
      
      // Filter by category if specified
      const filteredMilestones = category
        ? milestonesData.filter(m => m.template?.category === category)
        : milestonesData;
      
      setMilestones(filteredMilestones);
      logger.info(`Loaded ${filteredMilestones.length} milestones for couple ${couple.id}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load milestones';
      setError(errorMessage);
      logger.error('Error loading milestones:', err);
    } finally {
      setIsLoading(false);
    }
  }, [couple?.id, category, completedOnly]);

  const loadProgress = useCallback(async () => {
    if (!couple?.id) return;

    try {
      const progressData = await milestoneService.getMilestoneProgress(couple.id);
      setProgress(progressData);
      logger.info(`Loaded milestone progress for couple ${couple.id}`);
    } catch (err) {
      logger.error('Error loading milestone progress:', err);
      // Don't set error state for progress loading failures
    }
  }, [couple?.id]);

  const updateMilestone = useCallback(async (milestoneId: string, data: Record<string, any>) => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      setError(null);
      
      const updatedMilestone = await milestoneService.updateMilestone(
        milestoneId,
        data,
        user.id
      );
      
      // Update local state
      setMilestones(prev => 
        prev.map(m => m.id === milestoneId ? updatedMilestone : m)
      );
      
      // Refresh progress
      await loadProgress();
      
      logger.info(`Updated milestone ${milestoneId}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update milestone';
      setError(errorMessage);
      logger.error('Error updating milestone:', err);
      throw err;
    }
  }, [user?.id, loadProgress]);

  const getMilestoneByKey = useCallback((milestoneKey: string): CoupleMilestone | undefined => {
    return milestones.find(m => m.template?.milestone_key === milestoneKey);
  }, [milestones]);

  const getTemplateByKey = useCallback((milestoneKey: string): MilestoneTemplate | undefined => {
    return templates.find(t => t.milestone_key === milestoneKey);
  }, [templates]);

  const initializeMilestones = useCallback(async () => {
    if (!couple?.id) {
      throw new Error('No couple found');
    }

    try {
      setIsLoading(true);
      setError(null);
      
      const count = await milestoneService.initializeCoupleMilestones(couple.id);
      logger.info(`Initialized ${count} milestones for couple`);
      
      // Reload milestones after initialization
      await loadMilestones();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize milestones';
      setError(errorMessage);
      logger.error('Error initializing milestones:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [couple?.id, loadMilestones]);

  const syncFromOriginStory = useCallback(async (originStoryData: any) => {
    if (!couple?.id) {
      throw new Error('No couple found');
    }

    try {
      setError(null);
      
      await milestoneService.syncOriginStoryToMilestones(couple.id, originStoryData);
      
      // Reload milestones to reflect changes
      await loadMilestones();
      await loadProgress();
      
      logger.info('Synced origin story to milestones');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to sync origin story';
      setError(errorMessage);
      logger.error('Error syncing origin story:', err);
      throw err;
    }
  }, [couple?.id, loadMilestones, loadProgress]);

  const validateMilestoneData = useCallback((template: MilestoneTemplate, data: Record<string, any>): ValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!template.field_schema?.fields) {
      errors.push('Invalid template: missing field schema');
      return { isValid: false, errors, warnings };
    }

    // Validate each field in the template
    for (const field of template.field_schema.fields) {
      const value = data[field.key];
      
      // Check required fields
      if (field.required && (!value || (typeof value === 'string' && !value.trim()))) {
        errors.push(`${field.label} is required`);
        continue;
      }

      // Skip validation if field is empty and not required
      if (!value) continue;

      // Type-specific validation
      switch (field.type) {
        case 'date':
          if (typeof value === 'string' && isNaN(Date.parse(value))) {
            errors.push(`${field.label} must be a valid date`);
          }
          break;
        
        case 'number':
          if (typeof value !== 'number' && isNaN(Number(value))) {
            errors.push(`${field.label} must be a valid number`);
          }
          break;
        
        case 'select':
          if (field.options && !field.options.includes(value)) {
            errors.push(`${field.label} must be one of: ${field.options.join(', ')}`);
          }
          break;
        
        case 'text_array':
        case 'photo_array':
        case 'checkbox_array':
          if (!Array.isArray(value)) {
            errors.push(`${field.label} must be an array`);
          }
          break;
        
        case 'boolean':
          if (typeof value !== 'boolean') {
            errors.push(`${field.label} must be true or false`);
          }
          break;
      }
    }

    // Check for completion criteria
    const hasStory = data.story && data.story.trim();
    const hasDate = data.date || data.completion_date;
    
    if (!hasStory && !hasDate) {
      warnings.push('Consider adding a story or date to make this milestone more meaningful');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }, []);

  const refresh = useCallback(async () => {
    await loadAllData();
  }, [loadAllData]);

  // Computed values
  const totalMilestones = templates.length;
  const completedMilestones = milestones.filter(m => m.is_completed).length;
  const overallProgress = totalMilestones > 0 ? Math.round((completedMilestones / totalMilestones) * 100) : 0;

  const milestonesByCategory = milestones.reduce((acc, milestone) => {
    const category = milestone.template?.category || 'uncategorized';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(milestone);
    return acc;
  }, {} as Record<string, CoupleMilestone[]>);

  return {
    // Data
    templates,
    milestones,
    progress,
    
    // State
    isLoading,
    error,
    
    // Actions
    loadMilestones,
    loadTemplates,
    loadProgress,
    updateMilestone,
    getMilestoneByKey,
    getTemplateByKey,
    initializeMilestones,
    syncFromOriginStory,
    refresh,
    validateMilestoneData,
    
    // Computed values
    totalMilestones,
    completedMilestones,
    overallProgress,
    milestonesByCategory,
  };
};
