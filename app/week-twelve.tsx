import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, Switch } from 'react-native';

import { ArrowLeft, Save, CheckCircle, Heart, Users, MessageCircle, BookOpen, Calendar, MapPin, BookOpen as BookIcon, Activity, Sparkles } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useWeekTwelveData } from '../hooks/useWeekTwelveData';
import { colors } from '../utils/colors';

export default function WeekTwelveScreen() {
  const params = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  const [currentStoryPhase, setCurrentStoryPhase] = useState<'starter' | 'playerOne' | 'playerTwo' | 'final'>('starter');
  
  // Handle URL parameters for direct section navigation
  useEffect(() => {
    if (params.section) {
      const sectionIndex = parseInt(params.section as string);
      if (sectionIndex >= 0 && sectionIndex < steps.length) {
        setCurrentStep(sectionIndex);
      }
    }
  }, [params.section]);
  
  const {
    data,
    updateBuildAStoryGame,
    updateGetActiveDate,
    updateChatPrompt,
    updateSensateFocus,
    updateCompletedSections,
  } = useWeekTwelveData();

  const steps = [
    { title: 'Build-a-Story Game', icon: <BookIcon size={24} color="colors.white" /> },
    { title: 'Get Active Date', icon: <Calendar size={24} color="colors.white" /> },
    { title: 'Chat Prompts', icon: <MessageCircle size={24} color="colors.white" /> },
    { title: 'Sensate Focus', icon: <BookOpen size={24} color="colors.white" /> },
  ];

  const handleSaveAndContinue = () => {
    if (currentStep < steps.length - 1) {
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      setCurrentStep(currentStep + 1);
    } else {
      // All steps completed
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      Alert.alert('Congratulations!', 'You\'ve completed Week Twelve! All your responses have been saved to your scrapbook.');
    }
  };

  const handleComeBackLater = () => {
    router.back();
  };

  const handleStoryStarterSelect = (starter: string) => {
    updateBuildAStoryGame({ selectedStarter: starter });
    setCurrentStoryPhase('playerOne');
  };

  const handleStoryPhaseComplete = () => {
    if (currentStoryPhase === 'playerOne') {
      setCurrentStoryPhase('playerTwo');
    } else if (currentStoryPhase === 'playerTwo') {
      setCurrentStoryPhase('final');
    }
  };

  const handleSensateFocusToggle = (index: number, tried: boolean) => {
    updateSensateFocus(index, { tried });
  };

  const renderBuildAStoryGame = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Build-a-Story Game</Text>
        <Text style={styles.sectionSubtitle}>One partner starts, the other continues</Text>
      </View>

      <View style={styles.storyCard}>
        {currentStoryPhase === 'starter' && (
          <View>
            <Text style={styles.storyPhaseTitle}>Choose Your Story Starter</Text>
            <Text style={styles.storyPhaseSubtitle}>Select one of these prompts to begin your story</Text>
            
            {data.buildAStoryGame.storyStarters.map((starter, index) => (
              <TouchableOpacity
                key={index}
                style={styles.storyStarterOption}
                onPress={() => handleStoryStarterSelect(starter)}
              >
                <Text style={styles.storyStarterText}>{starter}</Text>
                <Sparkles size={20} color={colors.indigo} />
              </TouchableOpacity>
            ))}
          </View>
        )}

        {currentStoryPhase === 'playerOne' && (
          <View>
            <Text style={styles.storyPhaseTitle}>Player One's Turn</Text>
            <Text style={styles.storyStarterDisplay}>
              {data.buildAStoryGame.selectedStarter}
            </Text>
            <Text style={styles.storyPhaseSubtitle}>Continue the story...</Text>
            
            <TextInput
              placeholder="Continue the story from where it left off..."
              value={data.buildAStoryGame.playerOneContribution}
              onChangeText={(text) => updateBuildAStoryGame({ playerOneContribution: text })}
              multiline
              style={[styles.textInput, styles.storyInput, { minHeight: 120 }]}
            />
            
            <TouchableOpacity 
              style={styles.nextButton} 
              onPress={handleStoryPhaseComplete}
              disabled={!data.buildAStoryGame.playerOneContribution.trim()}
            >
              <Text style={styles.nextButtonText}>Player Two's Turn</Text>
            </TouchableOpacity>
          </View>
        )}

        {currentStoryPhase === 'playerTwo' && (
          <View>
            <Text style={styles.storyPhaseTitle}>Player Two's Turn</Text>
            <Text style={styles.storyStarterDisplay}>
              {data.buildAStoryGame.selectedStarter}
            </Text>
            <Text style={styles.storyContinuation}>
              {data.buildAStoryGame.playerOneContribution}
            </Text>
            <Text style={styles.storyPhaseSubtitle}>Continue the story...</Text>
            
            <TextInput
              placeholder="Continue the story from where it left off..."
              value={data.buildAStoryGame.playerTwoContribution}
              onChangeText={(text) => updateBuildAStoryGame({ playerTwoContribution: text })}
              multiline
              style={[styles.textInput, styles.storyInput, { minHeight: 120 }]}
            />
            
            <TouchableOpacity 
              style={styles.nextButton} 
              onPress={handleStoryPhaseComplete}
              disabled={!data.buildAStoryGame.playerTwoContribution.trim()}
            >
              <Text style={styles.nextButtonText}>Finalize Story</Text>
            </TouchableOpacity>
          </View>
        )}

        {currentStoryPhase === 'final' && (
          <View>
            <Text style={styles.storyPhaseTitle}>Your Complete Story</Text>
            
            <View style={styles.completeStory}>
              <Text style={styles.storyText}>
                {data.buildAStoryGame.selectedStarter}
              </Text>
              <Text style={styles.storyText}>
                {data.buildAStoryGame.playerOneContribution}
              </Text>
              <Text style={styles.storyText}>
                {data.buildAStoryGame.playerTwoContribution}
              </Text>
            </View>
            
            <Text style={styles.storyPhaseSubtitle}>Add a final touch to complete your story</Text>
            
            <TextInput
              placeholder="Add a final sentence or two to wrap up your story..."
              value={data.buildAStoryGame.finalStory}
              onChangeText={(text) => updateBuildAStoryGame({ finalStory: text })}
              multiline
              style={[styles.textInput, styles.storyInput, { minHeight: 80 }]}
            />
          </View>
        )}

        <View style={styles.timerOption}>
          <Text style={styles.timerLabel}>Use 3-minute timer for each turn?</Text>
          <Switch
            value={data.buildAStoryGame.timerUsed}
            onValueChange={(value) => updateBuildAStoryGame({ timerUsed: value })}
            trackColor={{ false: colors.borderLight, true: colors.indigo }}
            thumbColor={data.buildAStoryGame.timerUsed ? colors.white : colors.white}
          />
        </View>
      </View>
    </View>
  );

  const renderGetActiveDate = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Get Active Date</Text>
        <Text style={styles.sectionSubtitle}>Try a new sport/class/hobby together</Text>
      </View>

      <View style={styles.datePlanCard}>
        <Text style={styles.datePlanTitle}>Plan Your Active Adventure</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>When:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., This Sunday morning"
            value={data.getActiveDate.when}
            onChangeText={(text) => updateGetActiveDate({ when: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Where:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Local gym, Yoga studio, Hiking trail"
            value={data.getActiveDate.where}
            onChangeText={(text) => updateGetActiveDate({ where: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Activity:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Rock climbing, Dance class, Tennis lesson"
            value={data.getActiveDate.activity}
            onChangeText={(text) => updateGetActiveDate({ activity: text })}
          />
        </View>
      </View>
    </View>
  );

  const renderChatPrompts = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Chat Prompts</Text>
        <Text style={styles.sectionSubtitle}>Deep conversations to strengthen your bond</Text>
      </View>

      {data.chatPrompts.map((prompt, index) => (
        <View key={index} style={styles.chatPromptCard}>
          <Text style={styles.promptText}>{prompt.prompt}</Text>
          
          <View style={styles.answerInputs}>
            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player One:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerOneAnswer}
                onChangeText={(text) => updateChatPrompt(index, { playerOneAnswer: text })}
                multiline
              />
            </View>
            
            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player Two:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerTwoAnswer}
                onChangeText={(text) => updateChatPrompt(index, { playerTwoAnswer: text })}
                multiline
              />
            </View>
          </View>
        </View>
      ))}
    </View>
  );

  const renderSensateFocus = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Sensate Focus</Text>
        <Text style={styles.sectionSubtitle}>Step-by-step guided exercises for intimate connection</Text>
      </View>

      <View style={styles.toolkitInstructions}>
        <Text style={styles.instructionText}>
          Step-by-step guided exercises: Non-sexual touch, Sensory exploration, Guided breathing
        </Text>
        <Text style={styles.instructionText}>
          Couples mark which they tried + reflections
        </Text>
      </View>

      {data.sensateFocus.map((exercise, index) => (
        <View key={index} style={styles.sensateCard}>
          <View style={styles.exerciseHeader}>
            <Text style={styles.exerciseTitle}>{exercise.exercise}</Text>
            <View style={styles.triedToggle}>
              <Text style={styles.triedLabel}>Tried this?</Text>
              <Switch
                value={exercise.tried}
                onValueChange={(value) => handleSensateFocusToggle(index, value)}
                trackColor={{ false: colors.borderLight, true: colors.indigo }}
                thumbColor={exercise.tried ? colors.white : colors.white}
              />
            </View>
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Reflections:</Text>
            <TextInput
              style={[styles.textInput, { minHeight: 80 }]}
              placeholder="What did you experience? How did it feel? Any insights?"
              value={exercise.reflections}
              onChangeText={(text) => updateSensateFocus(index, { reflections: text })}
              multiline
            />
          </View>
        </View>
      ))}
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderBuildAStoryGame();
      case 1:
        return renderGetActiveDate();
      case 2:
        return renderChatPrompts();
      case 3:
        return renderSensateFocus();
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View
        style={[styles.header, { backgroundColor: colors.indigo }]}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="colors.white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Week 12: Playful Tales</Text>
        <Text style={styles.headerSubtitle}>Create stories together and explore intimate connection</Text>
      </View>

      {/* Progress Steps */}
      <View style={styles.progressContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {steps.map((step, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.progressStep,
                currentStep === index && styles.progressStepActive,
                data.completedSections[index] && styles.progressStepCompleted,
              ]}
              onPress={() => setCurrentStep(index)}
            >
              {data.completedSections[index] ? (
                <CheckCircle size={16} color="colors.white" />
              ) : (
                step.icon
              )}
              <Text style={[
                styles.progressStepText,
                currentStep === index && styles.progressStepTextActive,
                data.completedSections[index] && styles.progressStepTextCompleted,
              ]}>
                {step.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity style={styles.comeBackButton} onPress={handleComeBackLater}>
          <Text style={styles.comeBackButtonText}>Come Back Later</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.continueButton} onPress={handleSaveAndContinue}>
          <View
            style={[styles.continueButtonGradient, { backgroundColor: colors.indigo }]}
          >
            <Text style={styles.continueButtonText}>
              {currentStep < steps.length - 1 ? 'Save & Continue' : 'Complete Week'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundOrange,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  progressContainer: {
    backgroundColor: colors.white,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: colors.backgroundTertiary,
  },
  progressStepActive: {
    backgroundColor: colors.indigo,
  },
  progressStepCompleted: {
    backgroundColor: colors.success,
  },
  progressStepText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textSecondary,
    marginLeft: 6,
  },
  progressStepTextActive: {
    color: colors.white,
  },
  progressStepTextCompleted: {
    color: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionContainer: {
    marginTop: 24,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 24,
  },
  storyCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  storyPhaseTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
    textAlign: 'center',
  },
  storyPhaseSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 20,
    textAlign: 'center',
  },
  storyStarterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.backgroundGray,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  storyStarterText: {
    fontSize: 16,
    color: colors.textPrimary,
    flex: 1,
    marginRight: 12,
  },
  storyStarterDisplay: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.indigo,
    backgroundColor: colors.backgroundPink,
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  storyContinuation: {
    fontSize: 16,
    color: colors.textPrimary,
    backgroundColor: colors.backgroundGray,
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    lineHeight: 24,
  },
  storyInput: {
    minHeight: 120,
    textAlignVertical: 'top',
  },
  nextButton: {
    backgroundColor: colors.indigo,
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  nextButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  completeStory: {
    backgroundColor: colors.backgroundPink,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  storyText: {
    fontSize: 16,
    color: colors.blueDark,
    lineHeight: 24,
    marginBottom: 8,
  },
  timerOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.backgroundOrange,
    padding: 16,
    borderRadius: 12,
    marginTop: 20,
  },
  timerLabel: {
    fontSize: 16,
    color: colors.orangeDark,
    fontWeight: '500',
  },
  datePlanCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  datePlanTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  chatPromptCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  promptText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  answerInputs: {
    gap: 16,
  },
  answerInput: {
    flex: 1,
  },
  playerLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  toolkitInstructions: {
    backgroundColor: colors.backgroundPink,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 14,
    color: colors.indigoDark,
    marginBottom: 4,
    textAlign: 'center',
  },
  sensateCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  exerciseHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  exerciseTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.indigo,
    flex: 1,
  },
  triedToggle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  triedLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginRight: 8,
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    gap: 12,
  },
  comeBackButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderMedium,
    alignItems: 'center',
  },
  comeBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  continueButton: {
    flex: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  continueButtonGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
