/**
 * Login Screen - Optimized
 * 
 * Optimized version using shared authentication components.
 * Demonstrates significant code reduction and improved maintainability.
 * 
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft, Mail, Lock, Eye, EyeOff } from 'lucide-react-native';
import { useAuth } from '../hooks/useAuth';
import { colors } from '../utils/colors';
import { logger } from '../utils/logger';
import { simpleErrorService } from '../services/simpleErrorService';

// Import shared auth components
import {
  AuthScreenLayout,
  AuthHeader,
  AuthInput,
  AuthButton,
} from '../components/shared/AuthComponents';

export default function LoginScreen() {
  const { signIn, isLoading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  const handleLogin = async () => {
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    if (!email.includes('@')) {
      setError('Please enter a valid email address');
      return;
    }

    try {
      setError('');
      await signIn(email, password);
      logger.info('Login successful');
      router.replace('/(tabs)');
    } catch (error: any) {
      logger.error('Login error:', error);
      setError(error.message || 'Login failed. Please try again.');

      await simpleErrorService.reportError(error, {
        component: 'LoginScreen',
        action: 'handleLogin',
        metadata: { email: email },
      });
    }
  };



  const handleSignUp = () => {
    router.replace('/auth');
  };

  const handleForgotPassword = () => {
    Alert.alert(
      'Forgot Password',
      'Password reset functionality will be implemented soon. Please contact support for now.',
      [{ text: 'OK' }]
    );
  };

  return (
    <AuthScreenLayout backgroundColor={colors.primary}>
      {/* Back Button */}
      <View style={styles.backButtonContainer}>
        <AuthButton
          title=""
          onPress={() => router.back()}
          variant="ghost"
          style={styles.backButton}
        />
      </View>

      {/* Header */}
      <AuthHeader
        title="Welcome Back"
        subtitle="Sign in to continue your journey together"
      />

      {/* Form */}
      <View style={styles.formContainer}>
        <AuthInput
          value={email}
          onChangeText={setEmail}
          placeholder="Enter your email"
          label="Email"
          type="email"
          leftIcon={<Mail size={20} color="rgba(255, 255, 255, 0.7)" />}
          error={error}
        />

        <AuthInput
          value={password}
          onChangeText={setPassword}
          placeholder="Enter your password"
          label="Password"
          type="password"
          leftIcon={<Lock size={20} color="rgba(255, 255, 255, 0.7)" />}
          rightIcon={
            <AuthButton
              title=""
              onPress={() => setShowPassword(!showPassword)}
              variant="ghost"
              style={styles.eyeButton}
            />
          }
        />

        {/* Forgot Password */}
        <View style={styles.forgotPasswordContainer}>
          <AuthButton
            title="Forgot Password?"
            onPress={handleForgotPassword}
            variant="ghost"
            style={styles.forgotPasswordButton}
          />
        </View>

        {/* Login Button */}
        <AuthButton
          title="Sign In"
          onPress={handleLogin}
          variant="primary"
          loading={isLoading}
          style={styles.loginButton}
        />



        {/* Sign Up Link */}
        <View style={styles.signUpSection}>
          <Text style={styles.signUpText}>Don't have an account?</Text>
          <AuthButton
            title="Sign Up"
            onPress={handleSignUp}
            variant="ghost"
            style={styles.signUpButton}
          />
        </View>
      </View>
    </AuthScreenLayout>
  );
}

const styles = StyleSheet.create({
  backButtonContainer: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    paddingHorizontal: 20,
    flex: 1,
    justifyContent: 'center',
  },
  forgotPasswordContainer: {
    alignItems: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordButton: {
    paddingHorizontal: 0,
  },
  loginButton: {
    marginBottom: 24,
  },

  signUpSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signUpText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginRight: 8,
  },
  signUpButton: {
    paddingHorizontal: 0,
  },
  eyeButton: {
    width: 24,
    height: 24,
    padding: 0,
  },
});