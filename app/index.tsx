/**
 * Index Screen - Optimized
 * 
 * Optimized version with cleaner routing logic and better error handling.
 * Demonstrates improved maintainability and user experience.
 * 
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React, { useEffect, useState, useCallback } from 'react';
import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import { Redirect } from 'expo-router';
import { useAuth } from '../hooks/useAuth';
import { hasCompletedOnboarding } from '../utils/onboardingStorage';
import { setupGlobalErrorHandling, simpleErrorService } from '../services/simpleErrorService';
import { ErrorScreen } from '../components/ErrorHandler';
import { colors } from '../utils/colors';
import { useGlobalTheme } from '../components/shared/ThemeProvider';

// Import shared components
import { ScreenLayout } from '../components/shared';

type AppState = 'loading' | 'onboarding' | 'login' | 'main' | 'error';

export default function Index() {
  const { isAuthenticated, isLoading, isInitialized } = useAuth();
  const { currentTheme, isDarkMode } = useGlobalTheme();
  const [appState, setAppState] = useState<AppState>('loading');
  const [error, setError] = useState<string | null>(null);

  // Debug logging (remove in production)
  console.log('🔍 Index Debug:', {
    isAuthenticated,
    isLoading,
    isInitialized,
    appState,
    error
  });

  const determineAppState = useCallback(async (): Promise<AppState> => {
    try {
      // Check if user has completed onboarding
      const onboardingCompleted = await hasCompletedOnboarding();
      
      if (!onboardingCompleted) {
        return 'onboarding';
      }

      // TEMPORARY: Skip authentication check and go directly to main app
      // TODO: Re-enable proper authentication flow later
      return 'main';

      // Check authentication status (DISABLED TEMPORARILY)
      // if (isAuthenticated) {
      //   return 'main';
      // }
      // return 'login';
    } catch (err) {
      console.error('Error determining app state:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      
      // Report the error
      await simpleErrorService.reportError(err as Error, {
        component: 'IndexScreen',
        action: 'determineAppState',
        userId: isAuthenticated ? 'authenticated' : 'unauthenticated',
      });
      
      return 'error';
    }
  }, [isAuthenticated]);

  const initializeApp = useCallback(async () => {
    if (!isInitialized || isLoading) {
      return;
    }

    const newState = await determineAppState();
    setAppState(newState);
  }, [isInitialized, isLoading, determineAppState]);

  const handleRetry = useCallback(() => {
    setError(null);
    setAppState('loading');
    initializeApp();
  }, [initializeApp]);

  useEffect(() => {
    // Set up global error handling
    setupGlobalErrorHandling();
  }, []);

  useEffect(() => {
    initializeApp();
  }, [initializeApp]);

  // Error state
  if (appState === 'error' && error) {
    return (
      <ErrorScreen 
        error={error}
        onRetry={handleRetry}
      />
    );
  }

  // Loading state
  if (appState === 'loading' || !isInitialized || isLoading) {
    return (
      <ScreenLayout>
        <View style={[styles.loadingContainer, { backgroundColor: currentTheme.background }]}>
          <ActivityIndicator size="large" color={currentTheme.primary} />
          <Text style={[styles.loadingText, { color: currentTheme.text }]}>Loading...</Text>
        </View>
      </ScreenLayout>
    );
  }

  // Route based on app state
  switch (appState) {
    case 'onboarding':
      return <Redirect href="/onboarding" />;

    case 'login':
      // TEMPORARY: Redirect to main app instead of login
      return <Redirect href="/(tabs)" />;

    case 'main':
      return <Redirect href="/(tabs)" />;

    default:
      return <Redirect href="/onboarding" />;
  }
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    // Color will be applied dynamically via theme
  },
});