import React, { useEffect } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { ErrorHandler } from '../components/ErrorHandler';
import { AuthProvider } from '../hooks/useAuth';
import { SettingsProvider } from '../contexts/SettingsContext';
import { GlobalThemeProvider } from '../components/shared/ThemeProvider';
import { FavoritesProvider } from '../contexts/FavoritesContext';
import { attachAuthFlushListener } from '../lib/analytics/eventLogger';

export default function RootLayout() {
  useEffect(() => {
    const detach = attachAuthFlushListener();
    return () => detach?.();
  }, []);

  return (
    <ErrorHandler>
      <SettingsProvider>
        <GlobalThemeProvider>
          <AuthProvider>
            <FavoritesProvider>
              <Stack screenOptions={{ headerShown: false }}>
                <Stack.Screen name="index" />
                <Stack.Screen name="onboarding" />
                <Stack.Screen name="auth" />
                <Stack.Screen name="our-story" />
                <Stack.Screen name="couple-profile" />
                <Stack.Screen name="app-settings" />
                <Stack.Screen name="notifications" />
                <Stack.Screen name="scrapbook" />
                <Stack.Screen name="(tabs)" />
                <Stack.Screen name="+not-found" />
              </Stack>
              <StatusBar style="auto" />
            </FavoritesProvider>
          </AuthProvider>
        </GlobalThemeProvider>
      </SettingsProvider>
    </ErrorHandler>
  );
}
