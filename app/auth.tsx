import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import AuthScreen from '../components/AuthScreen';
import { colors } from '../utils/colors';
import { DSHeaderBar } from '../components/shared';

export default function Auth() {
  const handleBackPress = () => {
    router.back();
  };

  const handleAuthSuccess = () => {
    router.replace('/(tabs)');
  };

  return (
    <View style={styles.container}>
      <DSHeaderBar
        title="Create Account"
        tone="neutral"
        left={
          <TouchableOpacity onPress={handleBackPress}>
            <ArrowLeft size={24} color={colors.white} />
          </TouchableOpacity>
        }
      />
      
      <AuthScreen onAuthSuccess={handleAuthSuccess} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
});