/**
 * Home Screen - Clean Version
 *
 * Clean, maintainable version of the home screen using shared components and custom hooks.
 * This demonstrates the final refactored approach with separated concerns.
 *
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { useUserProfile } from '../../hooks/useUserProfile';
import { usePointsSystemSupabase } from '../../hooks/usePointsSystemSupabase';
import { useDateNightIdeasSupabase } from '../../hooks/useDateNightIdeasSupabase';
import { useEngagementSystem } from '../../hooks/useEngagementSystem';
import { colors } from '../../utils/colors';
import HamburgerMenu from '../../components/HamburgerMenu';
import { useHomeScreen } from '../../hooks/useHomeScreen';
import { secureStorage } from '../../utils/secureStorage';
import { useGlobalTheme } from '../../components/shared/ThemeProvider';

// Map stored icon ids to emoji for display
const mapIconToEmoji = (iconId?: string): string | undefined => {
  if (!iconId) return undefined;
  const map: Record<string, string> = {
    heart: '❤️',
    star: '⭐',
    flower: '🌸',
    moon: '🌙',
    sun: '☀️',
    sparkles: '✨',
    crown: '👑',
    rainbow: '🌈',
    diamond: '💎',
  };
  return map[iconId] || undefined;
};
// Import shared components
import {
  ProfileHeader,
  StatsOverview,
  FavoritesSection,
  AchievementsSection,
} from '../../components/shared';

export default function HomeScreen() {
  const { getCoupleNames, profile } = useUserProfile();
  const { totalPoints, achievements, level } = usePointsSystemSupabase();
  const { userIdeas } = useDateNightIdeasSupabase();
  const { challengeStreak } = useEngagementSystem();

  // Initialize home screen utilities (may expand over time)
  useHomeScreen();

  // State for profile pictures (local fallback)
  const [profilePictures, setProfilePictures] = useState<{partner1?: string, partner2?: string}>({});

  // Load local fallback profile pictures on mount
  useEffect(() => {
    loadProfilePictures();
  }, []);

  const loadProfilePictures = async () => {
    try {
      const stored = await secureStorage.getItem<string>('profile_pictures');
      if (stored) {
        setProfilePictures(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Error loading profile pictures:', error);
    }
  };

  // Calculate real statistics
  const currentStreak = challengeStreak?.currentStreak || 0;
  const yearsTogetherCount = calculateYearsTogether();
  const sharedAnswersCount = calculateSharedAnswers();
  const dateNightsCount = userIdeas?.filter(idea => idea.status === 'completed').length || 0;

  // Helper functions for calculations
  function calculateYearsTogether(): number {
    // Prefer relationshipStartDate if available; fallback to profile.createdAt
    const startIso = profile?.relationshipStartDate;
    const startDate = startIso ? new Date(startIso) : (profile?.createdAt ? new Date(profile.createdAt) : null);
    if (!startDate) return 0;
    const now = new Date();
    const diffYears = now.getFullYear() - startDate.getFullYear() - (
      now < new Date(startDate.getFullYear() + (now.getFullYear() - startDate.getFullYear()), startDate.getMonth(), startDate.getDate()) ? 1 : 0
    );
    return Math.max(0, diffYears);
  }

  function calculateSharedAnswers(): number {
    // This would ideally come from aggregating all week data
    // For now, use engagement stats or a reasonable estimate based on points
    return Math.floor((totalPoints || 0) / 10); // Rough estimate: 10 points per shared answer
  }

  // Get real favorite activities from user data
  const getFavoriteActivities = () => {
    const defaultActivities = [
      { emoji: '🍕', text: 'Pizza & Movie Nights' },
      { emoji: '🥾', text: 'Weekend Hiking Adventures' },
      { emoji: '🍳', text: 'Cooking Together' },
    ];

    // If we have completed date night ideas, use those as favorites
    const completedIdeas = userIdeas?.filter(idea => idea.status === 'completed' || idea.status === 'favorite').slice(0, 3);
    if (completedIdeas && completedIdeas.length > 0) {
      // This would need to be enhanced to get the actual idea details
      return defaultActivities; // For now, return defaults
    }

    return defaultActivities;
  };

  // Get real achievements
  const getRecentAchievements = () => {
    const defaultAchievements = [
      { emoji: '🏆', name: 'Connection Master', desc: `${currentStreak}-day streak completed!` },
      { emoji: '⭐', name: 'Level ' + (level || 1), desc: `Reached level ${level || 1}!` },
    ];

    // Use real achievements if available
    if (achievements && achievements.length > 0) {
      return achievements.slice(0, 2).map(achievement => ({
        emoji: achievement.type === 'streak' ? '🔥' : achievement.type === 'module' ? '⭐' : '🏆',
        name: achievement.title,
        desc: achievement.description,
      }));
    }

    return defaultAchievements;
  };

  return (
    <View style={styles.container}>
      <HamburgerMenu position="top-right" />

      {/* Profile Header */}
      <ProfileHeader
        names={getCoupleNames()}
        subtitle={`Together for ${yearsTogetherCount} beautiful years`}
        leftImage={profile.partner1PhotoUrl || profilePictures.partner1}
        rightImage={profile.partner2PhotoUrl || profilePictures.partner2}
        leftEmoji={mapIconToEmoji(profile?.partner1?.icon) || '👩🏻‍🦰'}
        rightEmoji={mapIconToEmoji(profile?.partner2?.icon) || '👨🏻‍🦱'}
      />

      {/* Scrollable Content Section */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Stats Overview */}
        <StatsOverview
          items={[
            { emoji: '💕', number: yearsTogetherCount, label: 'Years Together', iconBackgroundColor: colors.darkerPink },
            { emoji: '💬', number: sharedAnswersCount, label: 'Shared Answers', iconBackgroundColor: colors.primary },
            { emoji: '🔥', number: currentStreak, label: 'Day Streak', iconBackgroundColor: '#FFD700' },
            { emoji: '🌟', number: dateNightsCount, label: 'Date Nights', iconBackgroundColor: colors.secondary },
          ]}
        />

        {/* Favorites Section */}
        <FavoritesSection
          items={getFavoriteActivities()}
        />

        {/* Achievements Section */}
        <AchievementsSection
          items={getRecentAchievements()}
        />
      </ScrollView>
    </View>
  );
}



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },



  // Content styles
  content: {
    backgroundColor: colors.accentPink,
    flex: 1,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: 0,
  },
  scrollContent: {
    padding: 30,
    paddingBottom: 50, // Extra padding at bottom for better scrolling
  },




});