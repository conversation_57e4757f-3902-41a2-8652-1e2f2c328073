import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, Alert } from 'react-native';
import { ArrowLeft, Settings, Bell, Palette, Globe, Shield, Volume2, VolumeX, Vibrate, Moon, Sun, Smartphone } from 'lucide-react-native';
import { router } from 'expo-router';
import { useSettings } from '../contexts/SettingsContext';
import HamburgerMenu from '../components/HamburgerMenu';
import { DSHeaderBar, DSButton } from '../components/shared';
import { colors } from '../utils/colors';
import { useGlobalTheme } from '../components/shared/ThemeProvider';

export default function AppSettingsScreen() {
  const { settings, isLoading, updateSetting, resetSettings: contextResetSettings, isDarkMode, systemColorScheme } = useSettings();
  const { currentTheme: themeColors } = useGlobalTheme();

  // Simplified toggle handler
  const toggleSetting = async (key: keyof typeof settings) => {
    try {
      await updateSetting(key, !settings[key] as any);
    } catch (error) {
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    }
  };

  // Simplified alert handlers
  const showLanguageAlert = () => {
    const languages = [
      { text: 'English', value: 'English' },
      { text: 'Español', value: 'Español' },
      { text: 'Français', value: 'Français' },
      { text: 'Deutsch', value: 'Deutsch' },
    ];
    
    Alert.alert('Language', 'Select your preferred language:', [
      ...languages.map(lang => ({ 
        text: lang.text, 
        onPress: () => updateSetting('language', lang.value) 
      })),
      { text: 'Cancel', style: 'cancel' },
    ]);
  };

  const showFontSizeAlert = () => {
    const sizes: Array<'small' | 'medium' | 'large'> = ['small', 'medium', 'large'];
    Alert.alert('Font Size', 'Select your preferred font size:', [
      ...sizes.map((size) => ({
        text: size.charAt(0).toUpperCase() + size.slice(1),
        onPress: () => updateSetting('fontSize', size)
      })),
      { text: 'Cancel', style: 'cancel' },
    ]);
  };

  const showResetAlert = () => {
    Alert.alert('Reset Settings', 'Are you sure you want to reset all app settings to default?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Reset',
        style: 'destructive',
        onPress: async () => {
          try {
            await contextResetSettings();
            Alert.alert('Success', 'Settings reset to default values.');
          } catch (error) {
            Alert.alert('Error', 'Failed to reset settings. Please try again.');
          }
        }
      },
    ]);
  };

  // Theme helpers
  const getThemeDisplayText = () => {
    const themeMap = {
      light: 'Light',
      dark: 'Dark',
      auto: `Auto (${systemColorScheme === 'dark' ? 'Dark' : 'Light'})`
    };
    return themeMap[settings.theme] || 'Light';
  };

  const getThemeIcon = () => {
    if (settings.theme === 'auto') return <Smartphone size={20} color={themeColors.primary} />;
    return isDarkMode ? 
      <Moon size={20} color={themeColors.primary} /> : 
      <Sun size={20} color={themeColors.primary} />;
  };

  // Simplified setting item renderer
  type SettingItemProps = {
    icon: React.ReactNode;
    title: string;
    subtitle?: string;
    value: string | boolean;
    onPress: () => void;
    isToggle?: boolean;
  };
  const SettingItem: React.FC<SettingItemProps> = ({ icon, title, subtitle, value, onPress, isToggle = false }) => (
    <View style={styles.settingItem}>
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>{icon}</View>
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
      </View>

      {isToggle ? (
        <Switch
          value={Boolean(value)}
          onValueChange={onPress}
          trackColor={{ false: colors.borderLight, true: colors.lightPink }}
          thumbColor={value ? colors.lightPurple : colors.textTertiary}
        />
      ) : (
        <TouchableOpacity style={styles.settingAction} onPress={onPress}>
          <Text style={styles.settingValue}>{String(value)}</Text>
          <Text style={styles.settingArrow}>›</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  // Simplified theme option renderer
  const ThemeOption: React.FC<{ theme: 'light' | 'dark' | 'auto' }> = ({ theme }) => (
    <TouchableOpacity
      style={[styles.radioOption, { borderColor: themeColors.borderLight }]}
      onPress={() => updateSetting('theme', theme)}
    >
      <View style={[
        styles.radioCircle,
        { borderColor: themeColors.primary },
        settings.theme === theme && { backgroundColor: themeColors.primary }
      ]}>
        {settings.theme === theme && (
          <View style={[styles.radioInner, { backgroundColor: themeColors.background }]} />
        )}
      </View>
      <View style={styles.radioContent}>
        {getThemeIcon()}
        <Text style={[styles.radioLabel, { color: themeColors.textPrimary }]}>
          {theme.charAt(0).toUpperCase() + theme.slice(1)}
          {theme === 'auto' && ` (${systemColorScheme === 'dark' ? 'Dark' : 'Light'})`}
        </Text>
      </View>
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading settings...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: themeColors.background }]}>
      <HamburgerMenu position="top-right" />
      
      <DSHeaderBar
        title="App Settings"
        tone="pink"
        left={
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.white} />
          </TouchableOpacity>
        }
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Theme Section */}
        <View style={[styles.section, { backgroundColor: themeColors.backgroundSecondary }]}>
          <Text style={[styles.sectionTitle, { color: themeColors.textPrimary }]}>Theme</Text>
          <Text style={[styles.sectionDescription, { color: themeColors.textSecondary }]}>
            Choose your preferred theme
          </Text>
          <View style={styles.radioGroup}>
            {(['light', 'dark', 'auto'] as const).map((theme) => (
              <ThemeOption key={theme} theme={theme} />
            ))}
          </View>
        </View>

        {/* Other Settings */}
        <View style={[styles.section, { backgroundColor: themeColors.backgroundSecondary }]}>
          <Text style={[styles.sectionTitle, { color: themeColors.textPrimary }]}>Other Settings</Text>
          <SettingItem
            icon={<Globe size={20} color={colors.lightPurple} />}
            title="Language"
            subtitle="Choose your preferred language"
            value={settings.language}
            onPress={showLanguageAlert}
          />
        </View>

        {/* Data & Sync */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data & Sync</Text>
          <SettingItem
            icon={<Shield size={20} color={colors.blue} />}
            title="Auto Save"
            subtitle="Automatically save your progress"
            value={settings.autoSave}
            onPress={() => toggleSetting('autoSave')}
            isToggle
          />
          <SettingItem
            icon={<Settings size={20} color={colors.success} />}
            title="Data Sync"
            subtitle="Sync data across devices"
            value={settings.dataSync}
            onPress={() => toggleSetting('dataSync')}
            isToggle
          />
        </View>

        {/* Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          <SettingItem
            icon={<Bell size={20} color={colors.primary} />}
            title="Push Notifications"
            subtitle="Receive notifications for activities and reminders"
            value={settings.notifications}
            onPress={() => toggleSetting('notifications')}
            isToggle
          />
          <SettingItem
            icon={settings.soundEnabled ? 
              <Volume2 size={20} color={colors.success} /> : 
              <VolumeX size={20} color={colors.error} />
            }
            title="Sound"
            subtitle="Play sounds for notifications and interactions"
            value={settings.soundEnabled}
            onPress={() => toggleSetting('soundEnabled')}
            isToggle
          />
          <SettingItem
            icon={<Vibrate size={20} color={colors.secondary} />}
            title="Vibration"
            subtitle="Vibrate for notifications and feedback"
            value={settings.vibrationEnabled}
            onPress={() => toggleSetting('vibrationEnabled')}
            isToggle
          />
        </View>

        {/* Accessibility */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Accessibility</Text>
          <SettingItem
            icon={<Shield size={20} color={colors.warning} />}
            title="Accessibility Features"
            subtitle="Enable enhanced accessibility options"
            value={settings.accessibility}
            onPress={() => toggleSetting('accessibility')}
            isToggle
          />
          <SettingItem
            icon={<Palette size={20} color={colors.info} />}
            title="Font Size"
            subtitle={`Current: ${settings.fontSize}`}
            value={settings.fontSize}
            onPress={showFontSizeAlert}
          />
        </View>

        {/* Actions */}
        <View style={styles.section}>
          <DSButton
            title="Reset to Default"
            variant="outline"
            tone="warning"
            onPress={showResetAlert}
          />
        </View>

        {/* Info */}
        <View style={styles.infoCard}>
          <Settings size={24} color="#9CA3AF" />
          <Text style={styles.infoTitle}>Settings Saved</Text>
          <Text style={styles.infoText}>
            Your app settings are automatically saved and will persist across app restarts.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FEF3E2',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  sectionDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 4,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#6B7280',
  },
  settingAction: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValue: {
    fontSize: 16,
    color: '#6B7280',
    marginRight: 8,
  },
  settingArrow: {
    fontSize: 18,
    color: '#9CA3AF',
  },
  radioGroup: {
    gap: 12,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderRadius: 8,
    backgroundColor: 'transparent',
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  radioContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
    gap: 8,
  },
  radioLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  infoCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginTop: 12,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
});