import type { User as SupabaseUser, Session, AuthChangeEvent } from '@supabase/supabase-js';

// Centralized authentication types

export type AuthUser = SupabaseUser;
export type AuthSession = Session | null;
export type AuthEvent = AuthChangeEvent;

// App-level user profile (kept simple and UI-friendly)
export interface UserProfile {
  partner1: { name: string; icon: string };
  partner2: { name: string; icon: string };
  // Optional profile photo URLs stored in Supabase profiles table
  partner1PhotoUrl?: string | null;
  partner2PhotoUrl?: string | null;
  // Optional relationship start date (ISO date string 'YYYY-MM-DD')
  relationshipStartDate?: string | null;
  isComplete: boolean;
  createdAt: number;
  updatedAt: number;
}

// Auth API payloads
export interface SignUpData {
  email: string;
  password: string;
  partner1Name: string;
  partner1Icon: string;
  partner2Name: string;
  partner2Icon: string;
}

export interface SignInData {
  email: string;
  password: string;
}

// Common shapes
export interface AuthData {
  user: AuthUser;
  profile: UserProfile | null;
}

export interface AuthContextType {
  user: AuthUser | null;
  profile: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isInitialized: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (data: SignUpData) => Promise<void>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

export type OnAuthStateChangeCallback = (event: AuthEvent, session: AuthSession) => void;

