export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.4"
  }
  public: {
    Tables: {
      couple_milestones: {
        Row: {
          completed_by: string | null
          completion_date: string | null
          couple_id: string
          created_at: string | null
          id: string
          is_completed: boolean | null
          milestone_data: Json
          milestone_template_id: string
          timeline_event_id: string | null
          updated_at: string | null
        }
        Insert: {
          completed_by?: string | null
          completion_date?: string | null
          couple_id: string
          created_at?: string | null
          id?: string
          is_completed?: boolean | null
          milestone_data?: Json
          milestone_template_id: string
          timeline_event_id?: string | null
          updated_at?: string | null
        }
        Update: {
          completed_by?: string | null
          completion_date?: string | null
          couple_id?: string
          created_at?: string | null
          id?: string
          is_completed?: boolean | null
          milestone_data?: Json
          milestone_template_id?: string
          timeline_event_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "couple_milestones_milestone_template_id_fkey"
            columns: ["milestone_template_id"]
            isOneToOne: false
            referencedRelation: "milestone_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      couple_stories: {
        Row: {
          completed_sections: string[]
          couple_id: string
          created_at: string
          id: string
          last_updated: string
          last_updated_by: string | null
          story_data: Json
        }
        Insert: {
          completed_sections?: string[]
          couple_id: string
          created_at?: string
          id?: string
          last_updated?: string
          last_updated_by?: string | null
          story_data?: Json
        }
        Update: {
          completed_sections?: string[]
          couple_id?: string
          created_at?: string
          id?: string
          last_updated?: string
          last_updated_by?: string | null
          story_data?: Json
        }
        Relationships: [
          {
            foreignKeyName: "couple_stories_couple_fkey"
            columns: ["couple_id"]
            isOneToOne: true
            referencedRelation: "couples"
            referencedColumns: ["id"]
          },
        ]
      }
      couples: {
        Row: {
          couple_code: string
          created_at: string
          expires_at: string
          id: string
          partner1_user_id: string
          partner2_user_id: string | null
          qr_code_data: string | null
          status: string
        }
        Insert: {
          couple_code: string
          created_at?: string
          expires_at?: string
          id?: string
          partner1_user_id: string
          partner2_user_id?: string | null
          qr_code_data?: string | null
          status?: string
        }
        Update: {
          couple_code?: string
          created_at?: string
          expires_at?: string
          id?: string
          partner1_user_id?: string
          partner2_user_id?: string | null
          qr_code_data?: string | null
          status?: string
        }
        Relationships: []
      }
      date_night_ideas_global: {
        Row: {
          category: string | null
          cost: Database["public"]["Enums"]["cost_level"]
          created_at: string | null
          description: string
          difficulty: Database["public"]["Enums"]["difficulty_level"]
          emoji: string | null
          estimated_duration: number | null
          id: string
          indoor_outdoor: Database["public"]["Enums"]["location_type"]
          slug: string | null
          source: Database["public"]["Enums"]["idea_source"]
          title: string
          updated_at: string | null
          week_number: number | null
        }
        Insert: {
          category?: string | null
          cost: Database["public"]["Enums"]["cost_level"]
          created_at?: string | null
          description: string
          difficulty: Database["public"]["Enums"]["difficulty_level"]
          emoji?: string | null
          estimated_duration?: number | null
          id?: string
          indoor_outdoor: Database["public"]["Enums"]["location_type"]
          slug?: string | null
          source?: Database["public"]["Enums"]["idea_source"]
          title: string
          updated_at?: string | null
          week_number?: number | null
        }
        Update: {
          category?: string | null
          cost?: Database["public"]["Enums"]["cost_level"]
          created_at?: string | null
          description?: string
          difficulty?: Database["public"]["Enums"]["difficulty_level"]
          emoji?: string | null
          estimated_duration?: number | null
          id?: string
          indoor_outdoor?: Database["public"]["Enums"]["location_type"]
          slug?: string | null
          source?: Database["public"]["Enums"]["idea_source"]
          title?: string
          updated_at?: string | null
          week_number?: number | null
        }
        Relationships: []
      }
      date_night_ideas_user: {
        Row: {
          category: string | null
          cost: Database["public"]["Enums"]["cost_level"] | null
          created_at: string | null
          description: string | null
          difficulty: Database["public"]["Enums"]["difficulty_level"] | null
          emoji: string | null
          estimated_duration: number | null
          id: string
          indoor_outdoor: Database["public"]["Enums"]["location_type"] | null
          title: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          category?: string | null
          cost?: Database["public"]["Enums"]["cost_level"] | null
          created_at?: string | null
          description?: string | null
          difficulty?: Database["public"]["Enums"]["difficulty_level"] | null
          emoji?: string | null
          estimated_duration?: number | null
          id?: string
          indoor_outdoor?: Database["public"]["Enums"]["location_type"] | null
          title: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          category?: string | null
          cost?: Database["public"]["Enums"]["cost_level"] | null
          created_at?: string | null
          description?: string | null
          difficulty?: Database["public"]["Enums"]["difficulty_level"] | null
          emoji?: string | null
          estimated_duration?: number | null
          id?: string
          indoor_outdoor?: Database["public"]["Enums"]["location_type"] | null
          title?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      error_logs: {
        Row: {
          action: string | null
          component: string | null
          created_at: string | null
          error_message: string
          id: string
          metadata: Json | null
          session_id: string
          severity: string | null
          stack_trace: string | null
          url: string | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          action?: string | null
          component?: string | null
          created_at?: string | null
          error_message: string
          id: string
          metadata?: Json | null
          session_id: string
          severity?: string | null
          stack_trace?: string | null
          url?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          action?: string | null
          component?: string | null
          created_at?: string | null
          error_message?: string
          id?: string
          metadata?: Json | null
          session_id?: string
          severity?: string | null
          stack_trace?: string | null
          url?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      favorites: {
        Row: {
          created_at: string | null
          id: string
          item_id: string
          metadata: Json | null
          type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          item_id: string
          metadata?: Json | null
          type: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          item_id?: string
          metadata?: Json | null
          type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      meal_ideas_global: {
        Row: {
          category: string
          cook_time: number | null
          created_at: string | null
          description: string | null
          difficulty: string | null
          emoji: string | null
          id: string
          ingredients: string[] | null
          instructions: string[] | null
          link: string | null
          prep_time: number | null
          servings: number | null
          source: string | null
          tags: string[] | null
          title: string
          updated_at: string | null
          week_number: number | null
        }
        Insert: {
          category: string
          cook_time?: number | null
          created_at?: string | null
          description?: string | null
          difficulty?: string | null
          emoji?: string | null
          id?: string
          ingredients?: string[] | null
          instructions?: string[] | null
          link?: string | null
          prep_time?: number | null
          servings?: number | null
          source?: string | null
          tags?: string[] | null
          title: string
          updated_at?: string | null
          week_number?: number | null
        }
        Update: {
          category?: string
          cook_time?: number | null
          created_at?: string | null
          description?: string | null
          difficulty?: string | null
          emoji?: string | null
          id?: string
          ingredients?: string[] | null
          instructions?: string[] | null
          link?: string | null
          prep_time?: number | null
          servings?: number | null
          source?: string | null
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          week_number?: number | null
        }
        Relationships: []
      }
      meal_ideas_users: {
        Row: {
          category: string
          completed_at: string | null
          cook_time: number | null
          created_at: string | null
          description: string | null
          difficulty: string | null
          emoji: string | null
          id: string
          idea_id: string | null
          ingredients: string[] | null
          instructions: string[] | null
          is_completed: boolean | null
          is_favorite: boolean | null
          link: string | null
          prep_time: number | null
          servings: number | null
          source: string | null
          tags: string[] | null
          title: string
          updated_at: string | null
          user_id: string
          week_number: number | null
        }
        Insert: {
          category: string
          completed_at?: string | null
          cook_time?: number | null
          created_at?: string | null
          description?: string | null
          difficulty?: string | null
          emoji?: string | null
          id?: string
          idea_id?: string | null
          ingredients?: string[] | null
          instructions?: string[] | null
          is_completed?: boolean | null
          is_favorite?: boolean | null
          link?: string | null
          prep_time?: number | null
          servings?: number | null
          source?: string | null
          tags?: string[] | null
          title: string
          updated_at?: string | null
          user_id: string
          week_number?: number | null
        }
        Update: {
          category?: string
          completed_at?: string | null
          cook_time?: number | null
          created_at?: string | null
          description?: string | null
          difficulty?: string | null
          emoji?: string | null
          id?: string
          idea_id?: string | null
          ingredients?: string[] | null
          instructions?: string[] | null
          is_completed?: boolean | null
          is_favorite?: boolean | null
          link?: string | null
          prep_time?: number | null
          servings?: number | null
          source?: string | null
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          user_id?: string
          week_number?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "meal_ideas_users_idea_id_fkey"
            columns: ["idea_id"]
            isOneToOne: false
            referencedRelation: "meal_ideas_global"
            referencedColumns: ["id"]
          },
        ]
      }
      milestone_templates: {
        Row: {
          category: string
          created_at: string | null
          description: string | null
          display_order: number | null
          field_schema: Json
          id: string
          is_active: boolean | null
          is_core_milestone: boolean | null
          milestone_key: string
          title: string
          ui_config: Json | null
          updated_at: string | null
        }
        Insert: {
          category: string
          created_at?: string | null
          description?: string | null
          display_order?: number | null
          field_schema: Json
          id?: string
          is_active?: boolean | null
          is_core_milestone?: boolean | null
          milestone_key: string
          title: string
          ui_config?: Json | null
          updated_at?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          description?: string | null
          display_order?: number | null
          field_schema?: Json
          id?: string
          is_active?: boolean | null
          is_core_milestone?: boolean | null
          milestone_key?: string
          title?: string
          ui_config?: Json | null
          updated_at?: string | null
        }
        Relationships: []
      }
      origin_story: {
        Row: {
          best_memories_photos: Json | null
          biggest_challenge_photos: Json | null
          couple_id: string | null
          created_at: string | null
          data: Json
          first_kiss_photos: Json | null
          first_meeting_photos: Json | null
          id: string
          inside_jokes_photos: Json | null
          knew_loved_photos: Json | null
          last_updated: string | null
          most_romantic_photos: Json | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          best_memories_photos?: Json | null
          biggest_challenge_photos?: Json | null
          couple_id?: string | null
          created_at?: string | null
          data?: Json
          first_kiss_photos?: Json | null
          first_meeting_photos?: Json | null
          id?: string
          inside_jokes_photos?: Json | null
          knew_loved_photos?: Json | null
          last_updated?: string | null
          most_romantic_photos?: Json | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          best_memories_photos?: Json | null
          biggest_challenge_photos?: Json | null
          couple_id?: string | null
          created_at?: string | null
          data?: Json
          first_kiss_photos?: Json | null
          first_meeting_photos?: Json | null
          id?: string
          inside_jokes_photos?: Json | null
          knew_loved_photos?: Json | null
          last_updated?: string | null
          most_romantic_photos?: Json | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      pairing_attempts: {
        Row: {
          attempted_code: string
          created_at: string
          id: string
          success: boolean
          user_id: string
        }
        Insert: {
          attempted_code: string
          created_at?: string
          id?: string
          success?: boolean
          user_id: string
        }
        Update: {
          attempted_code?: string
          created_at?: string
          id?: string
          success?: boolean
          user_id?: string
        }
        Relationships: []
      }
      points_system: {
        Row: {
          achievements: Json
          created_at: string | null
          id: string
          last_activity: string | null
          level: number
          total_points: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          achievements?: Json
          created_at?: string | null
          id?: string
          last_activity?: string | null
          level?: number
          total_points?: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          achievements?: Json
          created_at?: string | null
          id?: string
          last_activity?: string | null
          level?: number
          total_points?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          created_at: string | null
          id: string
          is_complete: boolean
          partner1_icon: string
          partner1_name: string
          partner1_profile_picture: string | null
          partner2_icon: string
          partner2_name: string
          partner2_profile_picture: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id: string
          is_complete?: boolean
          partner1_icon?: string
          partner1_name?: string
          partner1_profile_picture?: string | null
          partner2_icon?: string
          partner2_name?: string
          partner2_profile_picture?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_complete?: boolean
          partner1_icon?: string
          partner1_name?: string
          partner1_profile_picture?: string | null
          partner2_icon?: string
          partner2_name?: string
          partner2_profile_picture?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      scrapbook: {
        Row: {
          created_at: string | null
          entries: Json
          id: string
          photos: Json | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          entries?: Json
          id?: string
          photos?: Json | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          entries?: Json
          id?: string
          photos?: Json | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      timeline_events: {
        Row: {
          couple_id: string
          created_at: string | null
          created_by: string | null
          description: string | null
          event_date: string
          event_type: string
          id: string
          is_featured: boolean | null
          is_visible: boolean | null
          metadata: Json | null
          source_id: string | null
          source_type: string
          title: string
          updated_at: string | null
        }
        Insert: {
          couple_id: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          event_date: string
          event_type: string
          id?: string
          is_featured?: boolean | null
          is_visible?: boolean | null
          metadata?: Json | null
          source_id?: string | null
          source_type: string
          title: string
          updated_at?: string | null
        }
        Update: {
          couple_id?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          event_date?: string
          event_type?: string
          id?: string
          is_featured?: boolean | null
          is_visible?: boolean | null
          metadata?: Json | null
          source_id?: string | null
          source_type?: string
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      timeline_photos: {
        Row: {
          caption: string | null
          created_at: string | null
          display_order: number | null
          file_size: number | null
          height: number | null
          id: string
          mime_type: string | null
          photo_url: string
          thumbnail_url: string | null
          timeline_event_id: string
          uploaded_by: string | null
          width: number | null
        }
        Insert: {
          caption?: string | null
          created_at?: string | null
          display_order?: number | null
          file_size?: number | null
          height?: number | null
          id?: string
          mime_type?: string | null
          photo_url: string
          thumbnail_url?: string | null
          timeline_event_id: string
          uploaded_by?: string | null
          width?: number | null
        }
        Update: {
          caption?: string | null
          created_at?: string | null
          display_order?: number | null
          file_size?: number | null
          height?: number | null
          id?: string
          mime_type?: string | null
          photo_url?: string
          thumbnail_url?: string | null
          timeline_event_id?: string
          uploaded_by?: string | null
          width?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "timeline_photos_timeline_event_id_fkey"
            columns: ["timeline_event_id"]
            isOneToOne: false
            referencedRelation: "timeline_events"
            referencedColumns: ["id"]
          },
        ]
      }
      user_events: {
        Row: {
          created_at: string | null
          event_name: string | null
          id: string
          metadata: Json
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          event_name?: string | null
          id?: string
          metadata?: Json
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          event_name?: string | null
          id?: string
          metadata?: Json
          user_id?: string | null
        }
        Relationships: []
      }
      weekly_data: {
        Row: {
          completed_at: string | null
          completed_sections: boolean[]
          created_at: string | null
          data: Json
          id: string
          updated_at: string | null
          user_id: string
          week_number: number
        }
        Insert: {
          completed_at?: string | null
          completed_sections?: boolean[]
          created_at?: string | null
          data?: Json
          id?: string
          updated_at?: string | null
          user_id: string
          week_number: number
        }
        Update: {
          completed_at?: string | null
          completed_sections?: boolean[]
          created_at?: string | null
          data?: Json
          id?: string
          updated_at?: string | null
          user_id?: string
          week_number?: number
        }
        Relationships: []
      }
    }
    Views: {
      error_stats: {
        Row: {
          affected_sessions: number | null
          affected_users: number | null
          component: string | null
          date: string | null
          error_count: number | null
          severity: string | null
        }
        Relationships: []
      }
      onboarding_progress: {
        Row: {
          completed: boolean | null
          intro_viewed: boolean | null
          journal_done: boolean | null
          last_event_at: string | null
          partner_done: boolean | null
          partner_invited: boolean | null
          ritual_done: boolean | null
          started: boolean | null
          user_id: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      check_pairing_attempts: {
        Args: { user_uuid: string }
        Returns: boolean
      }
      expire_old_couples: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      generate_couple_code: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_milestone_progress: {
        Args: { p_couple_id: string }
        Returns: {
          category: string
          completed_milestones: number
          completion_percentage: number
          total_milestones: number
        }[]
      }
      initialize_couple_milestones: {
        Args: { p_couple_id: string }
        Returns: number
      }
    }
    Enums: {
      cost_level: "free" | "low" | "medium" | "high"
      difficulty_level: "easy" | "medium" | "hard"
      idea_source: "weekly" | "system" | "user"
      location_type: "indoor" | "outdoor" | "both"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      cost_level: ["free", "low", "medium", "high"],
      difficulty_level: ["easy", "medium", "hard"],
      idea_source: ["weekly", "system", "user"],
      location_type: ["indoor", "outdoor", "both"],
    },
  },
} as const


// Friendly alias exports for app code
export type CostLevel = Enums<'cost_level'>
export type DifficultyLevel = Enums<'difficulty_level'>
export type LocationType = Enums<'location_type'>
export type IdeaSource = Enums<'idea_source'>

export type DateNightIdeaGlobal = Tables<'date_night_ideas_global'>
export type DateNightIdeaGlobalInsert = TablesInsert<'date_night_ideas_global'>
export type DateNightIdeaGlobalUpdate = TablesUpdate<'date_night_ideas_global'>
export type DateNightIdeaUser = Tables<'date_night_ideas_user'>
export type DateNightIdeaUserInsert = TablesInsert<'date_night_ideas_user'>
export type DateNightIdeaUserUpdate = TablesUpdate<'date_night_ideas_user'>

export interface DateNightFilters {
  category?: string
  cost?: CostLevel
  indoor_outdoor?: LocationType
  difficulty?: DifficultyLevel
  week_number?: number
  search?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  count: number
  hasMore: boolean
}

export interface ApiError {
  message: string
  code?: string
  status?: number
}
