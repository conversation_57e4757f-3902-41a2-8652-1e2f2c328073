export const APP_CONSTANTS = {
  // Input limits
  MAX_INPUT_LENGTH: parseInt(process.env.EXPO_PUBLIC_MAX_INPUT_LENGTH || '2000'),
  MAX_IMAGE_SIZE: parseInt(process.env.EXPO_PUBLIC_MAX_IMAGE_SIZE || '5242880'), // 5MB
  
  // Rate limiting
  RATE_LIMIT_ATTEMPTS: parseInt(process.env.EXPO_PUBLIC_RATE_LIMIT_ATTEMPTS || '10'),
  RATE_LIMIT_WINDOW_MS: parseInt(process.env.EXPO_PUBLIC_RATE_LIMIT_WINDOW || '60000'),
  
  // UI Constants
  PILL_RADIUS: 9999, // Semantic constant instead of magic number
  
  // Storage keys
  STORAGE_KEYS: {
    USER_PROFILE: 'user_profile',
    CONSENT: 'error_reporting_consent',
    SETTINGS: 'app_settings',
  } as const,
  
  // API timeouts
  API_TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
} as const;

export const VALIDATION_RULES = {
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
} as const;