import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from './logger';
import { logEvent } from '../lib/analytics/eventLogger';

const ONBOARDING_COMPLETED_KEY = 'onboarding_completed';

export async function hasCompletedOnboarding(): Promise<boolean> {
  try {
    // Simplified version - just check local storage for now
    const value = await AsyncStorage.getItem(ONBOARDING_COMPLETED_KEY);
    return value === 'true';
  } catch (error) {
    logger.error('Error checking onboarding status:', error);
    return false;
  }
}

export async function markOnboardingCompleted(): Promise<void> {
  try {
    // Always mark in local storage as backup
    await AsyncStorage.setItem(ONBOARDING_COMPLETED_KEY, 'true');
    
    // Log onboarding completion via central event logger (queues if guest)
    await logEvent('onboarding_completed');
  } catch (error) {
    logger.error('Error marking onboarding as completed:', error);
  }
}

export async function resetOnboarding(): Promise<void> {
  try {
    // Clear local storage
    await AsyncStorage.removeItem(ONBOARDING_COMPLETED_KEY);
    
    // No server delete; just local reset. If needed, server-side event removal can be added via admin tooling.
  } catch (error) {
    logger.error('Error resetting onboarding:', error);
  }
}
